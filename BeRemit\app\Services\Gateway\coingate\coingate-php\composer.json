{"name": "coingate/coingate-php", "type": "library", "description": "CoinGate library for PHP", "version": "3.0.4", "keywords": ["coingate", "bitcoin", "litecoin", "altcoin", "merchant", "gateway", "payment"], "homepage": "https://coingate.com", "license": "MIT", "authors": [{"name": "CoinGate and contributors", "homepage": "https://github.com/coingate/coingate-php/graphs/contributors"}], "require": {"ext-curl": "*", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "4.8.*"}, "autoload": {"psr-4": {"CoinGate\\": "lib/"}}}