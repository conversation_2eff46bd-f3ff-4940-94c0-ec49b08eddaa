#!/bin/bash

# Script to fix DomPDF permissions
# Run this script as root or with sudo

# Create necessary directories
mkdir -p storage/fonts
mkdir -p storage/app/pdf
mkdir -p storage/app/dompdf
mkdir -p storage/app/dompdf/fonts
mkdir -p public/fonts

# Set permissions
chmod -R 755 storage
chmod -R 755 public/fonts

# Set ownership to web server user (usually www-data)
# Change www-data to your web server user if different
chown -R www-data:www-data storage
chown -R www-data:www-data public/fonts

echo "Directories created and permissions set."
echo "DomPDF should now work correctly with Arabic fonts."
