<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSmsControlsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sms_controls', function (Blueprint $table) {
            $table->id();
            $table->string('actionMethod', 100);
            $table->string('actionUrl', 255);
            $table->string('headerData', 255)->nullable();
            $table->string('paramData', 255)->nullable();
            $table->string('formData', 255)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sms_controls');
    }
}
