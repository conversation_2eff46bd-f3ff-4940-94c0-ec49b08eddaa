name: "Run unit tests"

on:
  - push
  - pull_request

env:
  COMPOSER_MEMORY_LIMIT: -1

jobs:
  test:
    name: "Build"
    runs-on: ubuntu-latest
    strategy:
      max-parallel: 12
      matrix:
        php: ['7.1', '7.2', '7.3', '7.4', '8.0', '8.1']
        package-release: [dist]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Setup PHP ${{ matrix.php }}
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: exif,json,mbstring,dom

      - name: Install composer dependencies
        uses: ramsey/composer-install@v2
        with:
          dependency-versions: ${{ matrix.package-release }}

      - name: Run unit tests
        run: vendor/bin/phpunit

      - name: Run code sniffer
        run: vendor/bin/phpcs --standard=phpcs.xml src tests

      # - name: Upload to Scrutinizer
      #   continue-on-error: true
      #   run: |
      #     wget https://scrutinizer-ci.com/ocular.phar
      #     php ocular.phar code-coverage:upload --format=php-clover coverage.clover
