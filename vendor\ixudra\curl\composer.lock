{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at http://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "hash": "a22bdfa6600e7832d499e2f16f86176c", "packages": [{"name": "danielst<PERSON>les/stringy", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/danielstjules/Stringy.git", "reference": "3cf18e9e424a6dedc38b7eb7ef580edb0929461b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/danielstjules/Stringy/zipball/3cf18e9e424a6dedc38b7eb7ef580edb0929461b", "reference": "3cf18e9e424a6dedc38b7eb7ef580edb0929461b", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"Stringy\\": "src/"}, "files": ["src/Create.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "time": "2015-02-10 06:19:18"}, {"name": "doctrine/inflector", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "0bcb2e79d8571787f18b7eb036ed3d004908e604"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/0bcb2e79d8571787f18b7eb036ed3d004908e604", "reference": "0bcb2e79d8571787f18b7eb036ed3d004908e604", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "4.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Inflector\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common String Manipulations with regard to casing and singular/plural rules.", "homepage": "http://www.doctrine-project.org", "keywords": ["inflection", "pluralize", "singularize", "string"], "time": "2014-12-20 21:24:13"}, {"name": "illuminate/contracts", "version": "v5.0.0", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "78f1dba092d5fcb6d3a19537662abe31c4d128fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/78f1dba092d5fcb6d3a19537662abe31c4d128fd", "reference": "78f1dba092d5fcb6d3a19537662abe31c4d128fd", "shasum": ""}, "require": {"php": ">=5.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "time": "2015-01-30 16:27:08"}, {"name": "illuminate/support", "version": "v5.0.26", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "29e8618a45d090572e092abf193a257bf28c48d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/29e8618a45d090572e092abf193a257bf28c48d9", "reference": "29e8618a45d090572e092abf193a257bf28c48d9", "shasum": ""}, "require": {"danielstjules/stringy": "~1.8", "doctrine/inflector": "~1.0", "ext-mbstring": "*", "illuminate/contracts": "5.0.*", "php": ">=5.4.0"}, "suggest": {"jeremeamia/superclosure": "Required to be able to serialize closures (~2.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "http://laravel.com", "time": "2015-03-27 14:49:11"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.4.0"}, "platform-dev": []}