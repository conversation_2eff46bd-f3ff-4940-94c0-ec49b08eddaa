<?php

return [
    "GBP" => [
        'debit_uk_account' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'email' => [
                'value' => '',
                'label' => 'Email Address'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
            'fullname' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
        ]
    ],
    "NGN" => [
        'ussd' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'email' => [
                'value' => '',
                'label' => 'Email Address'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
            'fullname' => [
                'value' => '',
                'label' => 'full name'
            ],
        ],
        'debit_ng_account' => [
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'email' => [
                'value' => '',
                'label' => 'Email Address'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
            'fullname' => [
                'value' => '',
                'label' => 'full name'
            ],
        ],
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],
    "GHS" => [
        'mobile_money_ghana' => [
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'voucher' => [
                'value' => '',
                'label' => 'voucher'
            ],
            'email' => [
                'value' => '',
                'label' => 'Email Address'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
            'fullname' => [
                'value' => '',
                'label' => 'full name'
            ],
        ],
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],
    "KES" => [
        'mpesa' => [
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'email' => [
                'value' => '',
                'label' => 'Email Address'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
            'fullname' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
        ],
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],
    "UGX" => [
        'mobile_money_uganda' => [
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'email' => [
                'value' => '',
                'label' => 'Email Address'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ],
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],
    "TZS" => [
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],
    "ZAR" => [
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],
    "ZMW" => [
        'mobile_money_zambia' => [
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'email' => [
                'value' => '',
                'label' => 'Email Address'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ],
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],

    "XAF" => [
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],

    "XOF" => [
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],

    "SLL" => [
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],

    "CFA" => [
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],

    "RWF" => [
        'mobile_money_rwanda' => [

            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'email' => [
                'value' => '',
                'label' => 'Email Address'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
            'fullname' => [
                'value' => '',
                'label' => 'Recipient Name'
            ],
        ],
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],
    "TND" => [
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],
    "GNF" => [
        'transfers' => [
            'account_bank' => [
                'value' => '',
                'label' => 'Bank Code'
            ],
            'account_number' => [
                'value' => '',
                'label' => 'Bank Account Number'
            ],
            'amount' => [
                'value' => '',
                'label' => 'Amount'
            ],
            'narration' => [
                'value' => '',
                'label' => 'Beneficiary Name'
            ],
            'currency' => [
                'value' => '',
                'label' => 'Currency'
            ],
            'debit_currency' => [
                'value' => '',
                'label' => 'Debit Currency'
            ],
            'phone_number' => [
                'value' => '',
                'label' => 'Recipient Contact Number'
            ],
        ]
    ],
];
