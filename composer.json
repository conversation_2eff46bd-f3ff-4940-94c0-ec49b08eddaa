{"name": "larabp/larabp", "type": "project", "description": "Laravel Boilerplate with User Management, Admin Panel, KYC, Payment System, and Support Tickets.", "keywords": ["framework", "laravel", "boilerplate", "admin-panel", "user-management", "kyc", "payment-system"], "license": "MIT", "require": {"php": "^7.3|^8.0", "anhskohbo/no-captcha": "^3.5", "barryvdh/laravel-dompdf": "^0.9.0", "fideloper/proxy": "^4.4", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "hisorange/browser-detect": "^4.4", "intervention/image": "^2.5", "laravel/framework": "^8.12", "laravel/socialite": "^5.2", "laravel/tinker": "^2.5", "laravel/ui": "^3.2", "nexmo/laravel": "^2.4", "phpmailer/phpmailer": "^6.2", "propaganistas/laravel-phone": "^4.3", "pusher/pusher-php-server": "^4.1", "stevebauman/purify": "^4.0", "swiftmailer/swiftmailer": "^6.0"}, "require-dev": {"authorizenet/authorizenet": "dev-master#ef297d3e2d7c1092ddabd2c376eb485aac1aa66c", "facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/breeze": "^1.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helper/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}