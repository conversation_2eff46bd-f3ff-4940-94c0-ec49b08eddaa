<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Barryvdh\DomPDF\PDF;
use Dompdf\Dompdf;
use Dompdf\Options;

class DomPdfServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('dompdf.options', function () {
            $options = new Options();
            $options->set('defaultFont', 'Amiri');
            $options->set('isPhpEnabled', true);
            $options->set('isRemoteEnabled', true);
            $options->set('isHtml5ParserEnabled', true);
            $options->set('isFontSubsettingEnabled', true);
            $options->set('tempDir', storage_path('app/pdf'));
            $options->set('fontDir', storage_path('fonts/'));
            $options->set('fontCache', storage_path('app/dompdf/fonts/'));
            $options->set('chroot', public_path());
            $options->set('logOutputFile', storage_path('logs/pdf.log'));
            $options->set('debugKeepTemp', true);
            $options->set('debugCss', true);
            $options->set('debugLayout', true);
            return $options;
        });

        $this->app->singleton('dompdf', function () {
            $options = $this->app->make('dompdf.options');
            $dompdf = new Dompdf($options);
            $dompdf->setBasePath(public_path());
            return $dompdf;
        });

        $this->app->singleton('dompdf.wrapper', function ($app) {
            return new PDF($app['dompdf'], $app['config'], $app['files'], $app['view']);
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Register custom fonts
        $this->publishes([
            __DIR__.'/../../public/fonts' => public_path('fonts'),
        ], 'fonts');
    }
}
