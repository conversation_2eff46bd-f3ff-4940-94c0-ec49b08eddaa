document.addEventListener('DOMContentLoaded', function() {
    const phoneInputs = document.querySelectorAll('.phone-number');
    
    phoneInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            // Remove any non-digit characters except the first +
            let value = this.value.replace(/[^\d]/g, '');
            
            // Remove leading zeros
            value = value.replace(/^0+/, '');
            
            // Update input value
            this.value = value;
        });

        input.addEventListener('blur', function(e) {
            if (!this.value.match(/^[1-9][0-9]{6,14}$/)) {
                this.setCustomValidity('Please enter a valid phone number without + or 00');
            } else {
                this.setCustomValidity('');
            }
        });
    });
});