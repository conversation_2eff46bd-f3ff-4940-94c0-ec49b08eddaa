<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class CreateDomPdfDirectories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dompdf:create-directories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create necessary directories for DomPDF with proper permissions';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $directories = [
            storage_path('fonts'),
            storage_path('app/pdf'),
            storage_path('app/dompdf'),
            storage_path('app/dompdf/fonts'),
        ];

        foreach ($directories as $directory) {
            if (!File::exists($directory)) {
                File::makeDirectory($directory, 0755, true);
                $this->info("Created directory: {$directory}");
            } else {
                $this->info("Directory already exists: {$directory}");
            }

            // Ensure proper permissions
            chmod($directory, 0755);
            $this->info("Set permissions for: {$directory}");
        }

        $this->info('All directories created and permissions set.');
        
        return 0;
    }
}
