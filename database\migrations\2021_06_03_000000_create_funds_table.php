<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFundsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('funds', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->bigInteger('gateway_id')->nullable();
            $table->string('gateway_currency', 10)->nullable();
            $table->decimal('amount', 18, 8)->default(0);
            $table->decimal('charge', 18, 8)->default(0);
            $table->decimal('rate', 18, 8)->default(0);
            $table->decimal('final_amount', 18, 8)->default(0);
            $table->string('btc_amount', 50)->nullable();
            $table->string('btc_wallet', 100)->nullable();
            $table->string('transaction', 50)->unique();
            $table->string('try', 50)->nullable();
            $table->text('detail')->nullable();
            $table->boolean('status')->default(0)->comment('1=> Complete, 2=> Pending, 3=> Cancel');
            $table->text('feedback')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('funds');
    }
}
