<?php

namespace App\Services;

use Barryvdh\DomPDF\PDF;
use Dompdf\Dompdf;
use Dompdf\Options;

class PdfService
{
    /**
     * Generate a PDF with proper Arabic support
     *
     * @param string $view
     * @param array $data
     * @param string $filename
     * @return mixed
     */
    public static function generatePdf($view, $data, $filename = 'document.pdf')
    {
        /*
        $html = '        
        <html dir="rtl">
            <head>
                <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>            
                <style>
                @font-face {
                    font-family: "Amiri";
                   src: url("fonts/Amiri-Regular.ttf") format("truetype"); // Relative to chroot
                }

                @font-face {
                    font-family: "NotoNaskhArabic";
                   // src: url("' . storage_path('fonts/NotoNaskhArabic-Regular.ttf') . '") format("truetype");
                   src: url("fonts/NotoNaskhArabic-Regular.ttf") format("truetype"); // Relative to chroot
                }
                body {
                    direction: rtl;
                    unicode-bidi: embed;
                    font-family: Amiri;
                     letter-spacing: 0 !important;
                }                
            </style>
            </head>
            <body  dir="rtl">
                <div style="font-family: Amiri; direction: rtl">
                    اختبار نص عربي متصل
                </div>
            </body>
        </html>';
        */
        /*
        // Create DomPDF instance with options
        $options = new Options();
        $options->set('defaultFont', 'Amiri');//'NotoNaskhArabic'
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isFontSubsettingEnabled', false);
        $options->set('auto_language_detection', true);
        $options->set('isPhpEnabled', true);
        $options->set('tempDir', storage_path('app/dompdf/temp'));
        $options->set('fontDir', storage_path('fonts'));
        $options->set('fontCache', storage_path('fonts'));
        $options->set('chroot', storage_path());

        //dompdf = new Dompdf($options);
        $dompdf = new Dompdf($options);


        // Process HTML for better RTL support
        //$html = self::processHtmlForRtl($html);        
        // Load HTML into DomPDF
        //$dompdf->loadHtml($html, 'UTF-8');
        $dompdf->loadHtml($html);

        // Set paper size and orientation
        $dompdf->setPaper('A4', 'portrait');

        // Render PDF
        $dompdf->render();

        // Return PDF as stream
        //return $dompdf->stream($filename, ['Attachment' => false]);
        return $dompdf->stream($filename);
        */

        // Generate HTML content from view
        $html = view($view, $data)->render();

        $mpdf = new \Mpdf\Mpdf([
            'default_font' => 'Amiri',
            'mode' => 'utf-8', // ensure UTF-8 mode for Arabic
            'tempDir' => storage_path('app/mpdf/tmp'),
            'format'       => 'A4', // Explicitly set page format to A4
        ]);

        $mpdf->WriteHTML($html);
        return $mpdf->Output('document.pdf', 'I');
        
    }

    /**
     * Process HTML for better mixed language support
     *
     * @param string $html
     * @return string
     */
    private static function processHtmlForRtl($html)
    {
        // Add CSS for mixed language support
        $mixedLangCss = '<style>
            /* Add specific Arabic font support */
            @font-face {
                font-family: "Amiri";
                src: url("' . public_path('fonts/Amiri-Regular.ttf') . '") format("truetype");
                font-weight: normal;
                font-style: normal;
            }

            /* Wrap Arabic text in spans with class="arabic" */
            .arabic {
                font-family: "Amiri", "DejaVu Sans", sans-serif !important;
                direction: rtl !important;
                text-align: right !important;
                unicode-bidi: bidi-override !important;
            }
        </style>';

        /*
        // Insert mixed language CSS before closing head tag
        $html = str_replace('</head>', $mixedLangCss . '</head>', $html);

        // Process Arabic text in service provider field
        $html = preg_replace('/<td>([\x{0600}-\x{06FF}\s]+)<\/td>/u', '<td class="arabic">$1</td>', $html);
        */

        $rtlWrapper = '<div style="direction: rtl; font-family: Amiri, DejaVu Sans; text-align: right">';
        $html = str_replace(['<body>', '</body>'], ['<body>'.$rtlWrapper, '</div></body>'], $html);
                

        return $html;
    }
}
