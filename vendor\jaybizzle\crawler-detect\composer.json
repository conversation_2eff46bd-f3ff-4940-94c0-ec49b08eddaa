{"name": "jaybizzle/crawler-detect", "type": "library", "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "scripts": {"test": "vendor/bin/phpunit"}}