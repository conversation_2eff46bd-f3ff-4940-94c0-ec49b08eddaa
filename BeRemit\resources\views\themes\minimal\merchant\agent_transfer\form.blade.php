@extends($theme.'layouts.merchant')
@section('title', trans($title))

@section('content')
    <div class="container-fluid">
        <div class="main-page">
            <div class="row justify-content-center">
                <div class="col-xl-8">
                    <div class="card card-primary shadow">
                        <div class="card-header bg-primary">
                            <h3 class="text-white">@lang('Transfer to Agent')</h3>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('user.agent.transfer.process') }}" method="post">
                                @csrf
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="receiver_username">@lang('Agent Username')</label>
                                            <input type="text" name="receiver_username" id="receiver_username" value="{{ old('receiver_username') }}" class="form-control" placeholder="@lang('Enter agent username')" required>
                                            <div id="username-validation-message" class="mt-2"></div>
                                            @error('receiver_username')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="amount">@lang('Amount')</label>
                                            <div class="input-group">
                                                <input type="text" name="amount" id="amount" value="{{ old('amount') }}" class="form-control" placeholder="0.00" required>
                                                <div class="input-group-append">
                                                    <span class="input-group-text">{{ config('basic.currency') }}</span>
                                                </div>
                                            </div>
                                            @error('amount')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="note">@lang('Note') (@lang('Optional'))</label>
                                            <textarea name="note" id="note" class="form-control" rows="3" placeholder="@lang('Transfer note or description')">{{ old('note') }}</textarea>
                                            @error('note')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary btn-block">@lang('Transfer')</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4">
                    <div class="card card-primary shadow">
                        <div class="card-header bg-primary">
                            <h3 class="text-white">@lang('Transfer Information')</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between mb-3">
                                        <span>@lang('Available Balance'):</span>
                                        <span class="font-weight-bold">{{ getAmount(auth()->user()->balance) }} {{ config('basic.currency') }}</span>
                                    </div>

                                    <div class="d-flex justify-content-between mb-3">
                                        <span>@lang('Minimum Transfer'):</span>
                                        <span class="font-weight-bold">{{ getAmount(basicControl()->agent_transfer_min_amount) }} {{ config('basic.currency') }}</span>
                                    </div>

                                    <div class="d-flex justify-content-between mb-3">
                                        <span>@lang('Maximum Transfer'):</span>
                                        <span class="font-weight-bold">{{ getAmount(basicControl()->agent_transfer_max_amount) }} {{ config('basic.currency') }}</span>
                                    </div>

                                    <div class="d-flex justify-content-between mb-3">
                                        <span>@lang('Fixed Fee'):</span>
                                        <span class="font-weight-bold">{{ getAmount(basicControl()->agent_transfer_fixed_fee) }} {{ config('basic.currency') }}</span>
                                    </div>

                                    <div class="d-flex justify-content-between mb-3">
                                        <span>@lang('Percentage Fee'):</span>
                                        <span class="font-weight-bold">{{ getAmount(basicControl()->agent_transfer_percentage_fee) }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('css-lib')
@endpush

@push('extra-js')
@endpush

@push('js')
<script>
    'use strict';
    $(document).ready(function() {
        let validationTimeout;
        let isValidUsername = false;

        $('#receiver_username').on('input', function() {
            const username = $(this).val().trim();
            const messageDiv = $('#username-validation-message');

            // Clear previous timeout
            clearTimeout(validationTimeout);

            // Clear message if input is empty
            if (username.length === 0) {
                messageDiv.html('');
                isValidUsername = false;
                return;
            }

            // Show loading message
            messageDiv.html('<small class="text-info"><i class="fa fa-spinner fa-spin"></i> @lang("Checking username...")</small>');

            // Set timeout for validation
            validationTimeout = setTimeout(function() {
                validateUsername(username);
            }, 500); // Wait 500ms after user stops typing
        });

        function validateUsername(username) {
            $.ajax({
                url: '{{ route("user.agent.transfer.validate.username") }}',
                method: 'POST',
                data: {
                    username: username,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    const messageDiv = $('#username-validation-message');

                    if (response.valid) {
                        messageDiv.html('<small class="text-success"><i class="fa fa-check-circle"></i> ' + response.message + '</small>');
                        isValidUsername = true;
                    } else {
                        messageDiv.html('<small class="text-danger"><i class="fa fa-times-circle"></i> ' + response.message + '</small>');
                        isValidUsername = false;
                    }
                },
                error: function() {
                    $('#username-validation-message').html('<small class="text-danger"><i class="fa fa-times-circle"></i> @lang("Error validating username. Please try again.")</small>');
                    isValidUsername = false;
                }
            });
        }

        // Prevent form submission if username is not valid
        $('form').on('submit', function(e) {
            const username = $('#receiver_username').val().trim();

            if (username.length === 0) {
                e.preventDefault();
                $('#username-validation-message').html('<small class="text-danger"><i class="fa fa-times-circle"></i> @lang("Please enter an agent username.")</small>');
                return false;
            }

            if (!isValidUsername) {
                e.preventDefault();
                $('#username-validation-message').html('<small class="text-danger"><i class="fa fa-times-circle"></i> @lang("Please enter a valid agent username before proceeding.")</small>');
                return false;
            }
        });
    });
</script>
@endpush
