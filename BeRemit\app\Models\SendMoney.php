<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SendMoney extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];
    protected $casts = [
        'user_information' => 'object',
        'sender_identity_verification' => 'object',
        'sender_address_verification' => 'object',
        'paid_at' => 'datetime',
        'received_at' => 'datetime',
    ];

    protected $appends = ['totalPay','totalBaseAmountPay','totalBaseAmountChargePay'];

    public function getTotalPayAttribute()
    {
        return $this->payable_amount -  (float) $this->discount;
    }

    public function getTotalBaseAmountPayAttribute()
    {
       return (($this->totalPay / $this->send_curr_rate)* config('basic.rate')) ;
//       return (($this->totalPay)??1 / ($this->send_curr_rate) ??1) * config('basic.rate');
    }

    public function getTotalBaseAmountChargePayAttribute()
    {
        return ($this->fees < 1) ?: ($this->fees / $this->send_curr_rate);
    }



    public function user()
    {
        return $this->belongsTo(User::class,'user_id');
    }
    public function sendCurrency()
    {
        return $this->belongsTo(Country::class,'send_currency_id','id');
    }
    public function getCurrency()
    {
        return $this->belongsTo(Country::class,'receive_currency_id','id');
    }
    public function service()
    {
        return $this->belongsTo(Service::class,'service_id','id');
    }
    public function provider()
    {
        return $this->belongsTo(CountryService::class,'country_service_id','id');
    }

    public function payment()
    {
        return $this->hasOne(Fund::class,'send_money_id','id')->where('status','!=',0);
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class,'admin_id','id');
    }

    public function flutterTransactions()
    {
        return $this->hasMany(FlutterTransaction::class,'send_money_id')->latest();
    }

    /**
     * Get the merchant who paid out this remittance
     */
    public function payoutMerchant()
    {
        return $this->belongsTo(User::class, 'merchant_id', 'id');
    }

    /**
     * Get the sending merchant (if the sender is a merchant)
     */
    public function sendingMerchant()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->where('merchant', 1);
    }

    /**
     * Calculate net earnings for this remittance
     * Formula: Net Earnings = fees - merchant_commission - merchant_profit
     */
    public function getNetEarningsAttribute()
    {
        $totalFees = $this->fees ?? 0;
        $sendingMerchantShare = $this->merchant_commission ?? 0;
        $receivingMerchantShare = $this->merchant_profit ?? 0;

        return $totalFees - $sendingMerchantShare - $receivingMerchantShare;
    }

    /**
     * Get formatted net earnings
     */
    public function getFormattedNetEarningsAttribute()
    {
        return getAmount($this->net_earnings, config('basic.fraction_number'));
    }

    /**
     * Get formatted total fees
     */
    public function getFormattedTotalFeesAttribute()
    {
        return getAmount($this->fees, config('basic.fraction_number'));
    }

    /**
     * Get formatted sending merchant share
     */
    public function getFormattedSendingMerchantShareAttribute()
    {
        return getAmount($this->merchant_commission, config('basic.fraction_number'));
    }

    /**
     * Get formatted receiving merchant share
     */
    public function getFormattedReceivingMerchantShareAttribute()
    {
        return getAmount($this->merchant_profit, config('basic.fraction_number'));
    }

    /**
     * Get payment gateway from the associated payment
     */
    public function getPaymentGatewayAttribute()
    {
        if ($this->payment && $this->payment->gateway) {
            return $this->payment->gateway->name;
        }

        return $this->payment_type ?? 'Unknown';
    }

    /**
     * Scope to filter only completed remittances (status = 1)
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        if ($startDate && $endDate) {
            return $query->whereBetween('received_at', [$startDate, $endDate]);
        } elseif ($startDate) {
            return $query->where('received_at', '>=', $startDate);
        } elseif ($endDate) {
            return $query->where('received_at', '<=', $endDate);
        }

        return $query;
    }

    /**
     * Scope to filter by merchant (either sending or receiving)
     */
    public function scopeByMerchant($query, $merchantId)
    {
        if ($merchantId) {
            return $query->where(function($q) use ($merchantId) {
                $q->where('user_id', $merchantId)
                  ->orWhere('merchant_id', $merchantId);
            });
        }

        return $query;
    }

    /**
     * Scope to filter by payment gateway
     */
    public function scopeByGateway($query, $gateway)
    {
        if ($gateway) {
            return $query->whereHas('payment.gateway', function($q) use ($gateway) {
                $q->where('name', $gateway);
            })->orWhere('payment_type', $gateway);
        }

        return $query;
    }

    /**
     * Scope to filter by currency
     */
    public function scopeByCurrency($query, $currency)
    {
        if ($currency) {
            return $query->where(function($q) use ($currency) {
                $q->where('send_curr', $currency)
                  ->orWhere('receive_curr', $currency);
            });
        }

        return $query;
    }

}
