<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGatewaysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gateways', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->unique();
            $table->string('name', 100);
            $table->string('image')->nullable();
            $table->boolean('status')->default(1);
            $table->text('parameters')->nullable();
            $table->text('currencies')->nullable();
            $table->string('currency', 10)->nullable();
            $table->string('symbol', 10)->nullable();
            $table->decimal('min_amount', 18, 8)->default(0);
            $table->decimal('max_amount', 18, 8)->default(0);
            $table->decimal('percentage_charge', 8, 4)->default(0);
            $table->decimal('fixed_charge', 18, 8)->default(0);
            $table->decimal('convention_rate', 18, 8)->default(0);
            $table->text('extra_parameters')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gateways');
    }
}
