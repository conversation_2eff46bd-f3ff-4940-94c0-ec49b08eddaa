<?php

namespace App\Console\Commands;

use App\Models\SendMoney;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ExpireUnpaidRemittances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remittance:expire-unpaid';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expire unpaid remittances that are older than 30 minutes to protect against exchange rate changes';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Find remittances that are:
        // 1. In status 2 (pending)
        // 2. With payment_status 0 (unpaid)
        // 3. Created more than 30 minutes ago
        $thirtyMinutesAgo = Carbon::now()->subMinutes(30);
        
        $unpaidRemittances = SendMoney::where([
            'status' => 2,
            'payment_status' => 0
        ])
        ->where('created_at', '<', $thirtyMinutesAgo)
        ->get();
        
        $count = 0;
        foreach ($unpaidRemittances as $remittance) {
            // Update status to 3 (cancelled)
            $remittance->status = 3;
            $remittance->admin_reply = 'Automatically cancelled due to payment timeout (30 minutes)';
            $remittance->save();
            $count++;
        }
        
        $this->info("Successfully expired {$count} unpaid remittances.");
        return 0;
    }
}
