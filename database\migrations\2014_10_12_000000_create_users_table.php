<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('firstname', 50)->nullable();
            $table->string('lastname', 50)->nullable();
            $table->string('username', 50)->unique();
            $table->string('email', 90)->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('phone_code', 10)->nullable();
            $table->string('phone', 50)->nullable();
            $table->string('password');
            $table->decimal('balance', 18, 8)->default(0);
            $table->string('image')->nullable();
            $table->text('address')->nullable();
            $table->boolean('status')->default(1)->comment('0: banned, 1: active');
            $table->string('identity_verify')->nullable();
            $table->string('address_verify')->nullable();
            $table->boolean('two_fa')->default(0);
            $table->boolean('two_fa_verify')->default(1);
            $table->string('two_fa_code')->nullable();
            $table->boolean('email_verification')->default(0);
            $table->boolean('sms_verification')->default(0);
            $table->string('verify_code', 10)->nullable();
            $table->datetime('sent_at')->nullable();
            $table->datetime('last_login')->nullable();
            $table->string('last_seen')->nullable();
            $table->bigInteger('referral_id')->nullable();
            $table->string('language_id', 5)->default('en');
            $table->boolean('email_notification')->default(1);
            $table->boolean('sms_notification')->default(1);
            $table->boolean('push_notification')->default(1);
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
