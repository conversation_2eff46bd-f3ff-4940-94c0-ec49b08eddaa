<?php

namespace App\Http\Controllers\User;

use App\Helper\GoogleAuthenticator;
use App\Http\Controllers\Controller;
use App\Http\Traits\Notify;
use App\Http\Traits\Upload;

use App\Models\{Country,
    CountryService,
    Coupon,
    Fund,
    Gateway,
    IdentifyForm,
    KYC,
    Language,
    SendingPurpose,
    SendMoney,
    SourceFund,
    Template,
    Ticket,
    Transaction};
use Illuminate\Validation\Rules\Password;
use PDF;
use Carbon\Carbon;
use Illuminate\Support\Facades\{DB, Hash};
use Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use <PERSON><PERSON><PERSON>\Purify\Facades\Purify;
use Facades\App\Services\BasicService;

use hisorange\BrowserDetect\Parser as Browser;

class HomeController extends Controller
{
    use Upload, Notify;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->theme = template();
        $this->middleware(['auth']);
        $this->middleware(function ($request, $next) {
            $this->user = auth()->user();
            if($this->user->merchant == '1'){
                $this->theme .='merchant.';
            }else{
                $this->theme .='user.';
            }
            return $next($request);
        });
    }

    public function calculation(Request $request)
    {

        $this->validate($request, [
            'amount' => 'required|numeric',
            'sendCountry' => 'required|numeric',
            'getCountry' => 'required|numeric',
            'country_service' => 'required|numeric', // serviceId
            'payout_network' => 'required', //
            'sendReceive' => ['required', Rule::in(["send", "receive"])],
        ], [
            'sendCountry.required' => "Sender country is required",
            'getCountry.required' => "Please select a currency to receive",
            'country_service.required' => "Service is required",
            'payout_network.required' => "Provider must be required",
            'amount.required' => "Enter Amount",
        ]);


        $country = Country::select('id', 'name', 'slug', 'code', 'minimum_amount','maximum_amount', 'rate', 'facilities', 'image')->whereIn('id', [$request->sendCountry, $request->getCountry])->where('status', 1)->get();
        if ($request->has('sendCountry')) {
            $sendCountry = $country->where('id', $request->sendCountry)->first();
            if (!$sendCountry) {
                session()->flash('error', 'Sender Country Not Found');
                return back()->withInput();
            }

            if ($request->amount < $sendCountry->minimum_amount) {
                session()->flash('error', 'Minimum amount ' . getAmount($sendCountry->minimum_amount, config('basic.fraction_number')) . " " . $sendCountry->code);
                return back()->withInput();
            }

            if ($request->amount > $sendCountry->maximum_amount) {
                session()->flash('error', 'Maximum amount ' . getAmount($sendCountry->maximum_amount, config('basic.fraction_number')) . " " . $sendCountry->code);
                return back()->withInput();
            }
        }

        if ($request->has('getCountry')) {
            $receiveCountry = $country->where('id', $request->getCountry)->first();
            if (!$receiveCountry) {
                session()->flash('error', 'Receiver Country Not Found');
                return back()->withInput();
            }
            if (!$receiveCountry->facilities) {
                session()->flash('error', 'Receiver Country Service Not Available');
                return back()->withInput();
            }
            $receiveCountryFacilities = collect($receiveCountry->facilities)->where('id', $request->country_service)->first();
            if (!$receiveCountryFacilities) {
                session()->flash('error', 'Receiver Country Service Not Available');
                return back()->withInput();
            }


            $provider = CountryService::tobase()->where([
                'id' => $request->payout_network,
                'country_id' => $receiveCountry->id,
                'service_id' => $receiveCountryFacilities->id,
                'status' => 1
            ])->first();

            if (!$provider) {
                session()->flash('error', 'Provider must be required');
                return back()->withInput();
            }
        }


        $amount = $request->amount;
        $rate = $receiveCountry['rate'] / $sendCountry['rate'];


        $data['rate'] = round($rate, config('basic.fraction_number'));

        $data['send_currency'] = $sendCountry['code'];
        $data['receive_currency'] = $receiveCountry['code'];

        if ($request->sendReceive == "send") {
            $data['send_amount'] = $amount;
            $data['fees'] = round(getCharge($amount, $receiveCountry->id, $receiveCountryFacilities->id), 2);
            $data['total_payable'] = round($amount + $data['fees'], config('basic.fraction_number'));
            $data['recipient_get'] = round($amount * $rate, 2);
        }

        if ($request->sendReceive == "receive") {
            $data['send_amount'] = round($amount / $rate, 2);
            $data['fees'] = round(getCharge($amount, $receiveCountry->id, $receiveCountryFacilities->id) / $rate, 2);
            $data['total_payable'] = round(($amount / $rate) + $data['fees'], config('basic.fraction_number'));
            $data['recipient_get'] = round($amount, 2);
        }

        $invoice = invoice();


        $sendMoney = new  SendMoney();
        $sendMoney->invoice = $invoice;
        $sendMoney->user_id = $this->user->id;
        $sendMoney->send_currency_id = $sendCountry['id'];
        $sendMoney->receive_currency_id = $receiveCountry['id'];
        $sendMoney->service_id = $receiveCountryFacilities->id;
        $sendMoney->country_service_id = $provider->id;
        $sendMoney->send_curr_rate = $sendCountry['rate'];
        $sendMoney->send_curr = $sendCountry['code'];
        $sendMoney->receive_curr = $receiveCountry['code'];
        $sendMoney->rate = $rate;
        $sendMoney->send_amount = $data['send_amount'];
        $sendMoney->fees = $data['fees'];
        $sendMoney->payable_amount = $data['total_payable'];
        $sendMoney->recipient_get_amount = $data['recipient_get'];

        if($this->user->merchant == 0){
            $sendMoney->sender_name = $this->user->fullname;
            $sendMoney->sender_phone = $this->user->phone;
            $sendMoney->sender_email = $this->user->email;
            $sendMoney->sender_address = $this->user->address;
        }
        $sendMoney->save();

        return redirect()->route('user.sendMoney', $sendMoney);
    }

    public function sendMoney(SendMoney $sendMoney)
    {
        $user = $this->user;
        if ($sendMoney->user_id != $user->id) {
            abort(401);
        }
        if ($sendMoney->status != '0') {
            session()->flash('error', 'You are not eligible to change request.');
            return redirect()->route('user.transfer-log');
        }

        $data['page_title'] = "RECIPIENT DETAILS";
        $data['sendMoney'] = $sendMoney;
        $data['sourceFunds'] = SourceFund::select('title')->get();
        $data['sendingPurpose'] = SendingPurpose::select('title')->get();
        return view($this->theme . 'operation.recipient-form', $data);
    }

    public function sendMoneyFormData(SendMoney $sendMoney, Request $request)
    {
        $user = $this->user;
        if ($sendMoney->user_id != $user->id) {
            abort(401);
        }

        if ($sendMoney->status != '0') {
            session()->flash('error', 'You are not eligible to change request.');
            return redirect()->route('user.transfer-log');
        }
        $rules = [];

        $rules['recipient_name'] = ['sometimes','required', 'max:91'];
        $rules['recipient_contact_no'] = ['required', 'max:20'];
        $rules['recipient_email'] = ['nullable', 'email', 'max:30'];
        $rules['fund_source'] = ['required', 'max:255'];
        $rules['purpose'] = ['required', 'max:255'];
        $rules['promo_code'] = ['nullable', 'numeric'];
        $inputField = [];
        if (optional($sendMoney->provider)->services_form) {
            foreach ($sendMoney->provider->services_form as $key => $cus) {
                $rules[$key] = [$cus->validation];
                if ($cus->type == 'file') {
                    array_push($rules[$key], trim($cus->validation));
                    array_push($rules[$key], 'max:' . trim($cus->field_length));
                }
                if ($cus->type == 'text') {
                    array_push($rules[$key], trim($cus->validation));
                    if ($cus->length_type == 'max') {
                        array_push($rules[$key], 'max:' . trim($cus->field_length));
                    } elseif ($cus->length_type == 'digits') {
                        array_push($rules[$key], 'digits:' . trim($cus->field_length));
                    }
                }
                if ($cus->type == 'textarea') {
                    array_push($rules[$key], trim($cus->validation));
                    array_push($rules[$key], 'max:' . trim($cus->field_length));
                }
                $inputField[] = $key;
            }
        }


        $this->validate($request, $rules);
        $user = $this->user;


        $req = Purify::clean($request->all());
        $req = (object)$req;


        $path = config('location.send_money.path') . date('Y') . '/' . date('m') . '/' . date('d');
        $collection = collect($req);
        $reqField = [];
        if (optional($sendMoney->provider)->services_form) {
            foreach ($collection as $k => $v) {

                foreach (optional($sendMoney->provider)->services_form as $inKey => $inVal) {
                    if ($k != $inKey) {
                        continue;
                    } else {

                        if ($inVal->type == 'file') {
                            if ($request->hasFile($inKey)) {
                                try {
                                    $reqField[$inKey] = [
                                        'field_name' => $this->uploadImage($request[$inKey], $path),
                                        'file_location' => $path,
                                        'type' => $inVal->type,
                                    ];
                                } catch (\Exception $exp) {
                                    session()->flash('error', 'Could not upload your ' . $inKey);
                                    return back()->withInput();
                                }
                            }
                        } else {
                            $reqField[$inKey] = [
                                'field_name' => $v,
                                'type' => $inVal->type,
                            ];
                        }
                    }
                }
            }
            $sendMoney['user_information'] = $reqField;
        } else {
            $sendMoney['user_information'] = null;
        }
        $sendMoney->recipient_name = @$req->recipient_name;
        $sendMoney->recipient_contact_no = @$req->recipient_contact_no;
        $sendMoney->recipient_email = @$req->recipient_email;
        $sendMoney->fund_source = @$req->fund_source;
        $sendMoney->purpose = @$req->purpose;

        if ($request->promo_code != null) {
            $coupon = Coupon::where('code', trim($request->promo_code))->whereNull('user_id')->first();
            if (!$coupon) {
                session()->flash('error', 'Invalid promo code');
                return back()->withInput();
            }
            if ($sendMoney->promo_code == null) {
                $sendMoney->discount = ($sendMoney->payable_amount * $coupon->reduce_fee) / 100;
                $sendMoney->promo_code = $coupon->code;

                $coupon->user_id = $user->id;
                $coupon->used_at = Carbon::now();
                $coupon->update();
            }
        }


        $sendMoney->payment_type = 'online';
        $sendMoney->status = 2; //Draft
        $sendMoney->save();


        session()->put('invoice', $sendMoney->invoice);
        return redirect()->route('user.addFund');
    }

    public function sendMoneyAction(SendMoney $sendMoney, $actionType)
    {
        if (!in_array(strtolower($actionType), ['fillup', 'payment', 'details'])) {
            abort(404);
        }
        $user = $this->user;
        if ($sendMoney->user_id != $user->id) {
            abort(401);
        }
        if ($sendMoney->status == 0 && $sendMoney->payment_status == 0 && $actionType == "fillup") {
            return redirect()->route('user.sendMoney', $sendMoney);
        } else if ($sendMoney->status == 2 && $sendMoney->payment_status == 0 && $actionType == "payment") {
            session()->put('invoice', $sendMoney->invoice);
            return redirect()->route('user.addFund');
        } else if ($sendMoney->status != 0 && $actionType == "details") {
            $templateSection = ['contact-us'];
            $contactUs = Template::templateMedia()->whereIn('section_name', $templateSection)->get()->groupBy('section_name');
            $contactUs = $contactUs['contact-us'][0];

            $data['contact'] = [
                'email' => $contactUs->description->email,
                'phone' => $contactUs->description->phone,
                'address' => $contactUs->description->address
            ];


            $status = '';
            if ($sendMoney->status == 0 && $sendMoney->payment_status == 0) {
                $status = 'Information Need';
            } elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 0) {
                $status = 'Sender Not Pay Yet';
            } elseif ($sendMoney->status == 3 || $sendMoney->payment_status == 2) {
                $status = 'Cancelled';
            } elseif ($sendMoney->status == 1 && $sendMoney->payment_status == 1) {
                $status = 'Completed';
            } elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 1) {
                $status = 'Processing';
            } elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 3) {
                $status = 'Payment Hold';
            }

            $data['invoice'] = [
                'Transaction' => $sendMoney->invoice,
                'Status' => $status,
                'TransactionDate' => ($sendMoney->paid_at) ? dateTime($sendMoney->paid_at) : dateTime($sendMoney->created_at),
                'Service' => optional($sendMoney->service)->name,
                'ServiceProvider' => optional($sendMoney->provider)->name,
                'SendAmount' => getAmount($sendMoney->send_amount, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
                'Fees' => getAmount($sendMoney->fees, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
                'discountYes' => $sendMoney->discount,
                'Discount' => getAmount($sendMoney->discount, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
                'TotalSendAmount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
                'RecipientAmount' => getAmount($sendMoney->recipient_get_amount, config('basic.fraction_number')) . ' ' . $sendMoney->receive_curr,
                'Rate' => '1 ' . $sendMoney->send_curr . ' = ' . getAmount($sendMoney->rate, config('basic.fraction_number')) . ' ' . $sendMoney->receive_curr,

                'Sender' => [
                    'Name' => $sendMoney->sender_name,
                    'Phone' => $sendMoney->sender_phone,
                    'Address' => $sendMoney->sender_address,
                    'City' => $sendMoney->sender_city,
                    'PostCode' => $sendMoney->sender_post_code,
                    'Country' => $sendMoney->sender_country,
                ],
                'FundingSource' => $sendMoney->fund_source,
                'SendingPurpose' => $sendMoney->purpose,

                'Recipient' => [
                    'Name' => $sendMoney->recipient_name,
                    'Email' => $sendMoney->recipient_email,
                    'Phone' => $sendMoney->recipient_contact_no,
                ]
            ];



            // Use our custom PDF service for better Arabic support
            return \App\Services\PdfService::generatePdf(template(). 'layouts.invoice', $data, 'invoice.pdf');
        } else if ($sendMoney->status == '0') {
            session()->flash('error', 'You are not eligible to action this request.');
            return redirect()->route('user.transfer-log');
        }
        abort(404);

    }

    public function transferLog(Request $request)
    {
        $user = $this->user;
        $data['page_title'] = "TRANSFER LOG";
        $data['sendMoneys'] = SendMoney::where('user_id', $user->id)->latest()->paginate(config('basic.paginate'));
        return view($this->theme . 'operation.transferLog', $data);
    }

    public function transferLogDelete(Request $request, $id)
    {
        $sendMoney = SendMoney::withTrashed()->where('user_id',auth()->id())->findOrFail($id);
        if ($sendMoney->payment_status == 0) {
            $sendMoney->forceDelete();
            return back()->with('success', 'Transaction Has Been Removed');
        }
        return back()->with('error', 'Unable to remove Transaction');
    }


    public function recipients()
    {
        $user = $this->user;
        $data['page_title'] = "MY RECIPIENTS";
        $data['logs'] = SendMoney::select('recipient_name','recipient_email','recipient_contact_no')->where('user_id', $user->id)->where(['payment_status'=>1])->where('status','!=',0)->distinct()->latest()->paginate(config('basic.paginate'));
        return view($this->theme . 'operation.recipients', $data);
    }


    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = $this->user;
        if($this->user->merchant){
            $data['sendMoney']  = collect(SendMoney::where('user_id',$user->id)
                ->selectRaw('COUNT(CASE WHEN payment_status = 1 THEN id END) AS total')
                ->selectRaw('SUM(CASE WHEN payment_status = 1 THEN merchant_commission END) AS merchantCommission')
                ->get()->makeHidden(['totalPay', 'totalBaseAmountPay','totalBaseAmountChargePay'])->toArray())->collapse();

            $data['payout']  = collect(SendMoney::where('merchant_id',$user->id)
                ->selectRaw('COUNT(CASE WHEN status = 1 THEN id END) AS total')
                ->selectRaw('SUM(CASE WHEN status = 1 THEN merchant_profit END) AS merchantProfit')
                ->get()->makeHidden(['totalPay', 'totalBaseAmountPay','totalBaseAmountChargePay'])->toArray())->collapse();


            $data['funding'] = collect(Fund::where('user_id',$user->id)->selectRaw('SUM(CASE WHEN status = 1 THEN amount END) AS totalDeposit')->get()->toArray())->collapse();


            $data['transaction'] = Transaction::toBase()->where('user_id',$user->id)->count();
            $data['ticket'] = Ticket::toBase()->where('user_id',$user->id)->count();

            return view($this->theme . 'dashboard',$data);
        }

        return redirect()->route('user.transfer-log');
    }


    public function transaction()
    {
        $transactions = $this->user->transaction()->orderBy('id', 'DESC')->paginate(config('basic.paginate'));
        return view($this->theme . 'transaction.index', compact('transactions'));
    }

    public function transactionSearch(Request $request)
    {
        $search = $request->all();
        $dateSearch = $request->datetrx;
        $date = preg_match("/^[0-9]{2,4}\-[0-9]{1,2}\-[0-9]{1,2}$/", $dateSearch);
        $transaction = Transaction::where('user_id', $this->user->id)->with('user')
            ->when(@$search['transaction_id'], function ($query) use ($search) {
                return $query->where('trx_id', 'LIKE', "%{$search['transaction_id']}%");
            })
            ->when(@$search['remark'], function ($query) use ($search) {
                return $query->where('remarks', 'LIKE', "%{$search['remark']}%");
            })
            ->when($date == 1, function ($query) use ($dateSearch) {
                return $query->whereDate("created_at", $dateSearch);
            })
            ->paginate(config('basic.paginate'));
        $transactions = $transaction->appends($search);
        return view($this->theme . 'transaction.index', compact('transactions'));
    }

    public function fundHistory()
    {
        $funds = Fund::where('user_id', $this->user->id)->where('status', '!=', 0)->orderBy('id', 'DESC')->with('gateway')->paginate(config('basic.paginate'));
        return view($this->theme . 'transaction.fundHistory', compact('funds'));
    }

    public function fundHistorySearch(Request $request)
    {
        $search = $request->all();

        $dateSearch = $request->date_time;
        $date = preg_match("/^[0-9]{2,4}\-[0-9]{1,2}\-[0-9]{1,2}$/", $dateSearch);

        $funds = Fund::orderBy('id', 'DESC')->where('user_id', $this->user->id)->where('status', '!=', 0)
            ->when(isset($search['name']), function ($query) use ($search) {
                return $query->where('transaction', 'LIKE', $search['name']);
            })
            ->when($date == 1, function ($query) use ($dateSearch) {
                return $query->whereDate("created_at", $dateSearch);
            })
            ->when(isset($search['status']), function ($query) use ($search) {
                return $query->where('status', $search['status']);
            })
            ->with('gateway')
            ->paginate(config('basic.paginate'));
        $funds->appends($search);
        return view($this->theme . 'transaction.fundHistory', compact('funds'));
    }


    public function payNow()
    {
        $invoice = session()->get('invoice');

        if ($invoice == null) {
            abort(403);
        }
        $sendMoney = SendMoney::latest()->where(['invoice' => $invoice,'status'=>2])->with(['sendCurrency:id,name,rate'])->first();


        if (!$sendMoney) {
            return redirect()->route('user.transfer-log')->with('error', 'Invalid Payment Request');
        }

        if ($sendMoney->payment_status == 1) {
            return redirect()->route('user.transfer-log')->with('success', 'Payment has been completed');
        }
        if ($sendMoney->payment_status == 3) {
            return redirect()->route('user.transfer-log')->with('error', 'Payment has been rejected');
        }
        if ($sendMoney->payment_status == 3) {
            return redirect()->route('user.transfer-log')->with('warning', 'Wait for payment approval by admin');
        }
        $data['totalPayment'] = round($sendMoney->totalBaseAmountPay, config('basic.fraction_number'));
        $data['gateways'] = Gateway::where('status', 1)->orderBy('sort_by', 'ASC')->get();
        return view($this->theme . 'addFund', $data);
    }

    public function addFund()
    {
        $data['gateways'] = Gateway::where('status', 1)->orderBy('sort_by', 'ASC')->get();

        return view($this->theme . 'addFundWallet', $data);
    }


    public function profile(Request $request)
    {
        $validator = Validator::make($request->all(), []);
        $data['user'] = $this->user;
        $data['languages'] = Language::all();
        $data['identityFormList'] = IdentifyForm::where('status', 1)->get();
        if ($request->has('identity_type')) {
            $validator->errors()->add('identity', '1');
            $data['identity_type'] = $request->identity_type;
            $data['identityForm'] = IdentifyForm::where('slug', trim($request->identity_type))->where('status', 1)->firstOrFail();
            return view($this->theme . 'profile.myprofile', $data)->withErrors($validator);
        }
        return view($this->theme . 'profile.myprofile', $data);
    }

    public function updateProfile(Request $request)
    {
        $allowedExtensions = array('jpg', 'png', 'jpeg');
        $image = $request->image;
        $this->validate($request, [
            'image' => [
                'required',
                'max:4096',
                function ($fail) use ($image, $allowedExtensions) {
                    $ext = strtolower($image->extension());
                    if (!in_array($ext, $allowedExtensions)) {
                        return $fail("Only png, jpg, jpeg images are allowed");
                    } else {
                        if (($image->getSize() / 1000000) > 2) {
                            return $fail("Images MAX  2MB ALLOW!");
                        }
                    }

                }
            ]
        ]);
        $user = $this->user;
        if ($request->hasFile('image')) {
            $path = config('location.user.path');
            try {
                $user->image = $this->uploadImage($image, $path);
            } catch (\Exception $exp) {
                return back()->with('error', 'Could not upload your ' . $image)->withInput();
            }
        }
        $user->save();
        return redirect()->route('user.profile')->with('success', 'Updated Successfully.');
    }

    public function updateInformation(Request $request)
    {
        $req = Purify::clean($request->all());
        $user = $this->user;
        $rules = [
            'firstnameaaa' => 'required',
            'firstname' => 'required',
            'lastname' => 'required',
            'username' => "sometimes|required|alpha_dash|min:4|unique:users,username," . $user->id,
            'address' => 'required',
            // language_id removed - now controlled by header language switcher
        ];
        $message = [
            'firstname.required' => 'First Name field is required',
            'lastname.required' => 'Last Name field is required',
        ];

        $validator = Validator::make($request->all(), $rules, $message);
        if ($validator->fails()) {
            $validator->errors()->add('profile', '1');
            return redirect()->route('user.profile')->withErrors($validator)->withInput();
        }
        // language_id removed - now auto-updated by language switcher
        $user->firstname = $req['firstname'];
        $user->lastname = $req['lastname'];
        $user->username = $req['username'];
        $user->address = $req['address'];
        $user->save();
        return redirect()->route('user.profile')->with('success', 'Updated Successfully.');
    }

    public function updatePassword(Request $request)
    {

        $rules['current_password'] = ["required"];

        if (config('basic.strong_password') == 0) {
            $rules['password'] = ["required", "min:6", 'confirmed'];
        } else {
            $rules['password'] = ["required", 'confirmed',
                Password::min(6)->mixedCase()
                    ->letters()
                    ->numbers()
                    ->symbols()
                    ->uncompromised()];
        }

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $validator->errors()->add('password', '1');
            return back()->withErrors($validator)->withInput();
        }
        $user = $this->user;
        try {
            if (Hash::check($request->current_password, $user->password)) {
                $user->password = bcrypt($request->password);
                $user->save();
                return redirect()->route('user.profile')->with('success', 'Password Changes successfully.');
            } else {
                throw new \Exception('Current password did not match');
            }
        } catch (\Exception $e) {
            return redirect()->route('user.profile')->with('error', $e->getMessage());
        }
    }


    public function verificationSubmit(Request $request)
    {

        $identityFormList = IdentifyForm::where('status', 1)->get();
        $rules['identity_type'] = ["required", Rule::in($identityFormList->pluck('slug')->toArray())];
        $identity_type = $request->identity_type;
        $identityForm = IdentifyForm::where('slug', trim($identity_type))->where('status', 1)->firstOrFail();

        $params = $identityForm->services_form;

        $rules = [];
        $inputField = [];
        $verifyImages = [];

        if ($params != null) {
            foreach ($params as $key => $cus) {
                $rules[$key] = [$cus->validation];
                if ($cus->type == 'file') {
                    array_push($rules[$key], 'image');
                    array_push($rules[$key], 'mimes:jpeg,jpg,png');
                    array_push($rules[$key], 'max:2048');
                    array_push($verifyImages, $key);
                }
                if ($cus->type == 'text') {
                    array_push($rules[$key], 'max:191');
                }
                if ($cus->type == 'textarea') {
                    array_push($rules[$key], 'max:300');
                }
                $inputField[] = $key;
            }
        }

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $validator->errors()->add('identity', '1');

            return back()->withErrors($validator)->withInput();
        }


        $path = config('location.kyc.path').date('Y').'/'.date('m').'/'.date('d');
        $collection = collect($request);

        $reqField = [];
        if ($params != null) {
            foreach ($collection as $k => $v) {
                foreach ($params as $inKey => $inVal) {
                    if ($k != $inKey) {
                        continue;
                    } else {
                        if ($inVal->type == 'file') {
                            if ($request->hasFile($inKey)) {
                                try {
                                    $reqField[$inKey] = [
                                        'field_name' => $this->uploadImage($request[$inKey], $path),
                                        'type' => $inVal->type,
                                    ];
                                } catch (\Exception $exp) {
                                    session()->flash('error', 'Could not upload your ' . $inKey);
                                    return back()->withInput();
                                }
                            }
                        } else {
                            $reqField[$inKey] = $v;
                            $reqField[$inKey] = [
                                'field_name' => $v,
                                'type' => $inVal->type,
                            ];
                        }
                    }
                }
            }
        }

        try {

            DB::beginTransaction();

            $user = $this->user;
            $kyc = new KYC();
            $kyc->user_id = $user->id;
            $kyc->kyc_type = $identityForm->slug;
            $kyc->details = $reqField;
            $kyc->save();

            $user->identity_verify =  1;
            $user->save();

            if(!$kyc){
                DB::rollBack();
                $validator->errors()->add('identity', '1');
                return back()->withErrors($validator)->withInput()->with('error', "Failed to submit request");
            }
            DB::commit();
            return redirect()->route('user.profile')->withErrors($validator)->with('success', 'KYC request has been submitted.');

        } catch (\Exception $e) {
            return redirect()->route('user.profile')->withErrors($validator)->with('error', $e->getMessage());
        }
    }
    public function addressVerification(Request $request)
    {

        $rules = [];
        $rules['addressProof'] = ['image','mimes:jpeg,jpg,png', 'max:2048'];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $validator->errors()->add('addressVerification', '1');
            return back()->withErrors($validator)->withInput();
        }

        $path = config('location.kyc.path').date('Y').'/'.date('m').'/'.date('d');

        $reqField = [];
        try {
            if($request->hasFile('addressProof')){
                $reqField['addressProof'] = [
                    'field_name' => $this->uploadImage($request['addressProof'], $path),
                    'type' => 'file',
                ];
            }else{
                $validator->errors()->add('addressVerification', '1');

                session()->flash('error', 'Please select a ' . 'address Proof');
                return back()->withInput();
            }
        } catch (\Exception $exp) {
            session()->flash('error', 'Could not upload your ' . 'address Proof');
            return redirect()->route('user.profile')->withInput();
        }

        try {

            DB::beginTransaction();
            $user = $this->user;
            $kyc = new KYC();
            $kyc->user_id = $user->id;
            $kyc->kyc_type = 'address-verification';
            $kyc->details = $reqField;
            $kyc->save();
            $user->address_verify =  1;
            $user->save();

            if(!$kyc){
                DB::rollBack();
                $validator->errors()->add('addressVerification', '1');
                return redirect()->route('user.profile')->withErrors($validator)->withInput()->with('error', "Failed to submit request");
            }
            DB::commit();
            return redirect()->route('user.profile')->withErrors($validator)->with('success', 'Your request has been submitted.');

        } catch (\Exception $e) {
            $validator->errors()->add('addressVerification', '1');
            return redirect()->route('user.profile')->with('error', $e->getMessage())->withErrors($validator);
        }
    }

    public function twoStepSecurity()
    {
        $basic = (object)config('basic');
        $ga = new GoogleAuthenticator();
        $secret = $ga->createSecret();
        $qrCodeUrl = $ga->getQRCodeGoogleUrl($this->user->username . '@' . $basic->site_title, $secret);
        $previousCode = $this->user->two_fa_code;

        $previousQR = $ga->getQRCodeGoogleUrl($this->user->username . '@' . $basic->site_title, $previousCode);
        return view($this->theme . 'twoFA.index', compact('secret', 'qrCodeUrl', 'previousCode', 'previousQR'));
    }

    public function twoStepEnable(Request $request)
    {
        $user = $this->user;
        $this->validate($request, [
            'key' => 'required',
            'code' => 'required',
        ]);
        $ga = new GoogleAuthenticator();
        $secret = $request->key;
        $oneCode = $ga->getCode($secret);

        $userCode = $request->code;
        if ($oneCode == $userCode) {
            $user['two_fa'] = 1;
            $user['two_fa_verify'] = 1;
            $user['two_fa_code'] = $request->key;
            $user->save();
            $browser = new Browser();
            $this->mail($user, 'TWO_STEP_ENABLED', [
                'action' => 'Enabled',
                'code' => $user->two_fa_code,
                'ip' => request()->ip(),
                'browser' => $browser->browserName() . ', ' . $browser->platformName(),
                'time' => date('d M, Y h:i:s A'),
            ]);
            return back()->with('success', 'Two Factor has been enabled.');
        } else {
            return back()->with('error', 'Wrong Verification Code.');
        }

    }

    public function twoStepDisable(Request $request)
    {
        $this->validate($request, [
            'code' => 'required',
        ]);
        $user = $this->user;
        $ga = new GoogleAuthenticator();

        $secret = $user->two_fa_code;
        $oneCode = $ga->getCode($secret);
        $userCode = $request->code;

        if ($oneCode == $userCode) {
            $user['two_fa'] = 0;
            $user['two_fa_verify'] = 1;
            $user['two_fa_code'] = null;
            $user->save();
            $browser = new Browser();
            $this->mail($user, 'TWO_STEP_DISABLED', [
                'action' => 'Disabled',
                'ip' => request()->ip(),
                'browser' => $browser->browserName() . ', ' . $browser->platformName(),
                'time' => date('d M, Y h:i:s A'),
            ]);
            return back()->with('success', 'Two Factor has been disabled.');
        } else {
            return back()->with('error', 'Wrong Verification Code.');
        }
    }

    public function merchantSendMoney()
    {
        $user =  $this->user;
        if($user->merchant == 0){
            abort(404);
        }

        // Check if merchant has a country assigned
        if(!$user->country_id){
            session()->flash('error', 'You need to have a country assigned to your merchant account. Please contact support.');
            return redirect()->route('user.home');
        }

        $data['page_title'] = 'Send Money';
        $data['senderCurrencies'] = Country::select('id', 'name', 'slug', 'code', 'minimum_amount', 'rate', 'facilities', 'image')
            ->where('send_from', 1)
            ->where('status', 1)
            ->where('id', $user->country_id) // Only show the merchant's assigned country
            ->orderBy('name')
            ->get();
        $data['receiverCurrencies'] = Country::select('id', 'name', 'slug', 'code', 'minimum_amount', 'rate', 'facilities', 'image')->where('send_to', 1)->where('status', 1)->orderBy('name')->get();
        return view($this->theme. 'operation.send-form', $data);
    }


    public function merchantSendMoneyFormData(SendMoney $sendMoney, Request $request)
    {
        $basic = (object) config('basic');
        $user = $this->user;
        if ($sendMoney->user_id != $user->id) {
            abort(401);
        }

        if ($sendMoney->status != '0') {
            return back()->withInput()->with('error', 'You are not eligible to change request.');
        }


        $rules = [];

        $rules['payment_type'] = ["required", Rule::in(['online','fund'])];

//        $rules['sender_name'] = ['required', 'max:91'];
        $rules['first_name'] = ['required', 'max:91'];
        $rules['last_name'] = ['required', 'max:91'];
        $rules['sender_phone'] = ['required', 'max:50'];
        $rules['sender_email'] = ['nullable', 'max:50'];
        $rules['sender_address'] = ['nullable', 'max:191'];
        $rules['sender_city'] = ['nullable', 'max:40'];
        $rules['sender_post_code'] = ['nullable', 'max:20'];
        $rules['sender_country'] = ['nullable', 'max:20'];

        $rules['addressProof'] = ['nullable','image', 'mimes:jpeg,jpg,png','max:4096'];
        $rules['identityProof'] = ['nullable','image', 'mimes:jpeg,jpg,png','max:4096'];
        $rules['identity_type'] = ["nullable", Rule::in(['Driving License','Passport','National ID'])];

        $rules['recipient_name'] = ['sometimes','required', 'max:91'];
        $rules['recipient_contact_no'] = ['sometimes','required', 'max:20'];
        $rules['recipient_email'] = ['sometimes','nullable', 'email', 'max:30'];
        $rules['fund_source'] = ['sometimes','nullable', 'max:255'];
        $rules['purpose'] = ['sometimes','nullable', 'max:255'];
        $rules['promo_code'] = ['sometimes','nullable', 'numeric'];
        $inputField = [];
        if (optional($sendMoney->provider)->services_form) {
            foreach ($sendMoney->provider->services_form as $key => $cus) {
                $rules[$key] = [$cus->validation];
                if ($cus->type == 'file') {
                    array_push($rules[$key], trim($cus->validation));
                    array_push($rules[$key], 'max:' . trim($cus->field_length));
                }
                if ($cus->type == 'text') {
                    array_push($rules[$key], trim($cus->validation));
                    if ($cus->length_type == 'max') {
                        array_push($rules[$key], 'max:' . trim($cus->field_length));
                    } elseif ($cus->length_type == 'digits') {
                        array_push($rules[$key], 'digits:' . trim($cus->field_length));
                    }
                }
                if ($cus->type == 'textarea') {
                    array_push($rules[$key], trim($cus->validation));
                    array_push($rules[$key], 'max:' . trim($cus->field_length));
                }
                $inputField[] = $key;
            }
        }


        $this->validate($request, $rules,[
            'sender_phone.required' =>'Phone number field is required.',
            'sender_email.required' =>'Email field is required.',
            'sender_address.required' =>'Address field is required.',
            'sender_city.required' =>'City/Town field is required.',
            'sender_post_code.required' =>'Post Code field is required.',
            'sender_country.required' =>'Country field is required.',
            'payment_type.required' =>'Please select a payment Method.',
        ]);


        if ($request->payment_type == 'fund' && $sendMoney->totalBaseAmountPay > $user->balance) {
            return back()->withInput()->with('error', 'Insufficient Balance.');
        }



        $req = Purify::clean($request->all());
        $req = (object) $req;


        $path = config('location.send_money.path') . date('Y') . '/' . date('m') . '/' . date('d');
        $collection = collect($req);

        DB::beginTransaction();

        $reqField = [];
        if (optional($sendMoney->provider)->services_form) {
            foreach ($collection as $k => $v) {
                foreach (optional($sendMoney->provider)->services_form as $inKey => $inVal) {
                    if ($k != $inKey) {
                        continue;
                    } else {

                        if ($inVal->type == 'file') {
                            if ($request->hasFile($inKey)) {
                                try {
                                    $reqField[$inKey] = [
                                        'field_name' => $this->uploadImage($request[$inKey], $path),
                                        'file_location' => $path,
                                        'type' => $inVal->type,
                                    ];
                                } catch (\Exception $exp) {
                                    session()->flash('error', 'Could not upload your ' . $inKey);
                                    return back()->withInput();
                                }
                            }
                        } else {
                            $reqField[$inKey] = [
                                'field_name' => $v,
                                'type' => $inVal->type,
                            ];
                        }
                    }
                }
            }
            $sendMoney['user_information'] = $reqField;
        } else {
            $sendMoney['user_information'] = null;
        }
        $sendMoney->recipient_name = isset($req->recipient_name) ? $req->recipient_name : null;
        $sendMoney->recipient_contact_no = isset($req->recipient_contact_no) ? $req->recipient_contact_no : null;
        $sendMoney->recipient_email = isset($req->recipient_email) ? $req->recipient_email : null;
        $sendMoney->fund_source = isset($req->fund_source) ? $req->fund_source : null;
        $sendMoney->purpose = isset($req->purpose) ? $req->purpose : null;

        if ($request->promo_code != null) {
            $coupon = Coupon::where('code', trim($request->promo_code))->whereNull('user_id')->first();
            if (!$coupon) {
                session()->flash('error', 'Invalid promo code');
                return back()->withInput();
            }
            if ($sendMoney->promo_code == null) {
                $sendMoney->discount = ($sendMoney->payable_amount * $coupon->reduce_fee) / 100;
                $sendMoney->promo_code = $coupon->code;

                $coupon->user_id = $user->id;
                $coupon->used_at = Carbon::now();
                $coupon->update();
            }
        }

        $sendMoney->sender_name = $req->first_name . ' '. $req->last_name;
        $sendMoney->sender_phone = isset($req->sender_phone) ? $req->sender_phone : null;
        $sendMoney->sender_email = isset($req->sender_email) ? $req->sender_email : null;
        $sendMoney->sender_address = isset($req->sender_address) ? $req->sender_address : null;
        $sendMoney->sender_city = isset($req->sender_city) ? $req->sender_city : null;
        $sendMoney->sender_post_code = isset($req->sender_post_code) ? $req->sender_post_code : null;
        $sendMoney->sender_state = isset($req->sender_state) ? $req->sender_state : null;
        $sendMoney->sender_country = isset($req->sender_country) ? $req->sender_country : null;
        $sendMoney->payment_type = $req->payment_type;

        $identityField = [];
        $sendMoney->sender_identity_type = isset($req->identity_type) ? $req->identity_type : null;

        if($request->hasFile('identityProof')){
            try {
                $identityField['identityProof'] = [
                    'field_name' => $this->uploadImage($request['identityProof'], $path),
                    'file_location' => $path,
                    'type' => 'file',
                ];
                $sendMoney->sender_identity_verification = $identityField;
            } catch (\Exception $exp) {
                session()->flash('error', 'Could not upload your identity Proof');
                return back()->withInput();
            }
        }


        $addressField = [];
        if($request->hasFile('addressProof')){
            try {
                $addressField['addressProof'] = [
                    'field_name' => $this->uploadImage($request['addressProof'], $path),
                    'file_location' => $path,
                    'type' => 'file',
                ];
                $sendMoney->sender_address_verification = $addressField;
            } catch (\Exception $exp) {
                session()->flash('error', 'Could not upload your address Proof');
                return back()->withInput();
            }
        }



        $merchantCom = 0;
        if( 0 < $sendMoney->fees){
            $basicCom = config('basic.merchant_commission');  // percent
            $baseCharge = $sendMoney->fees / $sendMoney->send_curr_rate;
            $merchantCom = ($baseCharge * $basicCom)/100;

            $sendMoney->merchant_commission = $merchantCom;
            $sendMoney->admin_commission  = ($baseCharge-$merchantCom);

        }else{
            $sendMoney->merchant_commission = 0;
            $sendMoney->admin_commission  = 0;
        }


        $sendMoney->status = 2; //Draft
        $sendMoney->update();

        if($request->payment_type == 'online'){

            if(!$sendMoney){
                DB::rollBack();
                return back()->withInput()->with('error', "Failed to submit request");
            }
            DB::commit();
            session()->put('invoice', $sendMoney->invoice);
            return redirect()->route('user.addFund');

        }elseif($request->payment_type == 'fund'){
            $sendMoney->payment_status = 1;
            $sendMoney->paid_at = Carbon::now();
            $sendMoney->save();

            $user->balance -=  $sendMoney->totalBaseAmountPay;
            $user->save();

            $trx_id = strRandom();
            $remarks = "Send money Invoice: ".$sendMoney->invoice;
            BasicService::makeTransaction($user, getAmount($sendMoney->totalBaseAmountPay),  getAmount($sendMoney->totalBaseAmountChargePay), '-', $trx_id, $remarks );

            $remarks = "You got commission from #".$sendMoney->invoice;
            BasicService::makeTransaction($user, getAmount($merchantCom),  0, '+', $trx_id, $remarks );

            // Send SMS notification to recipient
            if (!empty($sendMoney->recipient_contact_no)) {
                $this->sendSms($sendMoney->recipient_contact_no, 'REMITTANCE_SENT', [
                    'invoice' => $sendMoney->invoice
                ], 4);
            }


            if(!$sendMoney){
                DB::rollBack();
                return back()->withInput()->with('error', "Failed to submit request");
            }
            DB::commit();

            $msg = [
                'username' => $user->username,
                'amount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')),
                'currency' => $sendMoney->send_curr
            ];
            $action = [
                "link" => route('admin.money-transfer.details', $sendMoney),
                "icon" => "fa fa-money-bill-alt text-white"
            ];
            $this->adminPushNotification('SEND_MONEY_REQUEST', $msg, $action);


            $msg2 = [
                'amount' =>  getAmount($sendMoney->totalPay, config('basic.fraction_number')),
                'currency' => $sendMoney->send_curr,
            ];
            $action2 = [
                "link" => '#',
                "icon" => "fas fa-money-bill-alt text-white"
            ];
            $this->userPushNotification($user, 'MERCHANT_TRANSFER_PROCESSING', $msg2, $action2);


            $this->sendMailSms($user, 'MERCHANT_TRANSFER_PROCESSING', [
                'amount' => getAmount($sendMoney->totalPay,config('basic.fraction_number')),
                'currency' => $sendMoney->send_curr,
                'invoice' => $sendMoney->invoice
            ]);
        }

        return redirect()->route('user.transfer-log')->with('success', 'Transaction has been Successful.');
    }

    public function merchantPayout()
    {
        $user =  $this->user;
        if($user->merchant == 0){
            abort(404);
        }

        // Check if merchant has a country assigned
        if(!$user->country_id){
            session()->flash('error', 'You need to have a country assigned to your merchant account. Please contact support.');
            return redirect()->route('user.home');
        }

        $data['page_title'] = 'Payout Money';
        return view($this->theme. 'operation.payout', $data);
    }

    public function merchantPayoutRequest(Request $request)
    {
        $this->validate($request, [
            'invoice_number' => 'required|numeric|min:10',
        ]);
        $user =  $this->user;

        $sendMoney = SendMoney::where('invoice', $request->invoice_number)->where('payment_status',1)->first();
        if(!$sendMoney){
            return back()->withInput()->with('error', "Invalid Request");
        }
        if ($sendMoney->user_id == $user->id) {
            session()->flash('error', ' You cannot payout this transaction');
            return back();
        }
        if($sendMoney->status == '1') {
            session()->flash('error', ' This Transaction already Closed');
            return back();
        }
        if($sendMoney->status == '2') {
            return redirect()->route('user.merchant.payout-money.info',$sendMoney);
        }

        abort(404);
    }

    public function payoutRequestInfo(SendMoney $sendMoney)
    {

        $user =  $this->user;
        if($user->merchant == 0){
            abort(404);
        }

        if($sendMoney->payment_status != '1'){
            return back()->withInput()->with('error', "Invalid Request");
        }
        if ($sendMoney->user_id == $user->id) {
            session()->flash('error', ' You cannot payout this transaction');
            return back();
        }



        if($sendMoney->status == '1' && ($sendMoney->merchant_id != $user->id)) {
            session()->flash('error', ' This Transaction already Closed');
            return back();
        }

        // Check if the receive_currency_id matches the merchant's country_id
        if($sendMoney->receive_currency_id != $user->country_id) {
            session()->flash('error', 'You can only process payouts for transactions to your assigned country');
            return back();
        }

        if($sendMoney->status == '2' || ($sendMoney->status == '1' && $sendMoney->merchant_id == $user->id)) {
            $data['page_title'] = 'Receipt Preview';
            $templateSection = ['contact-us'];
            $contactUs = Template::templateMedia()->whereIn('section_name', $templateSection)->get()->groupBy('section_name');
            $contactUs = $contactUs['contact-us'][0];

            $data['contact'] = [
                'email' => $contactUs->description->email,
                'phone' => $contactUs->description->phone,
                'address' => $contactUs->description->address
            ];


            $status = '';
            if ($sendMoney->status == 0 && $sendMoney->payment_status == 0) {
                $status = 'Information Need';
            } elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 0) {
                $status = 'Sender Not Pay Yet';
            } elseif ($sendMoney->status == 3 || $sendMoney->payment_status == 2) {
                $status = 'Cancelled';
            } elseif ($sendMoney->status == 1 && $sendMoney->payment_status == 1) {
                $status = 'Completed';
            } elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 1) {
                $status = 'Processing';
            } elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 3) {
                $status = 'Payment Hold';
            }

            $data['invoice'] = [
                'Transaction' => $sendMoney->invoice,
                'Status' => $status,
                'TransactionDate' => ($sendMoney->paid_at) ? dateTime($sendMoney->paid_at) : dateTime($sendMoney->created_at),
                'Service' => optional($sendMoney->service)->name,
                'ServiceProvider' => optional($sendMoney->provider)->name,
                'SendAmount' => getAmount($sendMoney->send_amount, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
                'Fees' => getAmount($sendMoney->fees, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
                'discountYes' => $sendMoney->discount,
                'Discount' => getAmount($sendMoney->discount, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
                'TotalSendAmount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
                'RecipientAmount' => getAmount($sendMoney->recipient_get_amount, config('basic.fraction_number')) . ' ' . $sendMoney->receive_curr,
                'Rate' => '1 ' . $sendMoney->send_curr . ' = ' . getAmount($sendMoney->rate, config('basic.fraction_number')) . ' ' . $sendMoney->receive_curr,

                'Sender' => [
                    'Name' => $sendMoney->sender_name,
                    'Phone' => $sendMoney->sender_phone,
                    'Address' => $sendMoney->sender_address,
                ],
                'FundingSource' => $sendMoney->fund_source,
                'SendingPurpose' => $sendMoney->purpose,

                'Recipient' => [
                    'Name' => $sendMoney->recipient_name,
                    'Email' => $sendMoney->recipient_email,
                    'Phone' => $sendMoney->recipient_contact_no,
                ]
            ];


            $data['sendMoney'] = $sendMoney;

            return view($this->theme. 'operation.payout-info', $data);
        }
        abort(404);
    }

    public function payoutRequestConfirm(Request $request, SendMoney $sendMoney)
    {
        $user =  $this->user;
        if($user->merchant == 0){
            abort(404);
        }
        if($sendMoney->payment_status != '1'){
            return back()->withInput()->with('error', "Invalid Request");
        }
        if ($sendMoney->user_id == $user->id) {
            session()->flash('error', ' You cannot payout this transaction');
            return back();
        }

        if($sendMoney->status == '1') {
            session()->flash('error', ' This Transaction already Closed');
            return back();
        }

        // Check if the receive_currency_id matches the merchant's country_id
        if($sendMoney->receive_currency_id != $user->country_id) {
            session()->flash('error', 'You can only process payouts for transactions to your assigned country');
            return back();
        }

        if($sendMoney->status == '2') {

            $merchantCom = 0;
            if( 0 < $sendMoney->fees){
                $basicCom = config('basic.merchant_profit');  // percent
                $baseCharge = $sendMoney->fees / $sendMoney->send_curr_rate;
                $merchantCom = ($baseCharge * $basicCom)/100;
                $sendMoney->admin_commission  = $baseCharge - ($sendMoney->merchant_commission + $merchantCom);
                $sendMoney->merchant_profit = $merchantCom;
            }
            $sendMoney->received_at = Carbon::now();
            $sendMoney->status = 1;
            $sendMoney->merchant_id = $user->id;
            $sendMoney->save();

			//this code results wrong Balance Increase for Receiving Merchant, since his balance should only increased by send amount converted to base currency
            //$user->balance +=  $sendMoney->totalBaseAmountPay;
            //$user->save();

			$payoutAmount = $sendMoney->send_amount / $sendMoney->send_curr_rate; // Remittance amount only
			$user->balance += $payoutAmount; // Credit only the remittance amount
			$user->save();


            $trx_id = strRandom();
            $remarks = "Your account has been credited for payout #".$sendMoney->invoice;
            BasicService::makeTransaction($user, getAmount($payoutAmount),  0, '+', $trx_id, $remarks );



            $user->balance +=  $merchantCom;
            $user->save();


            $remarks2 = "Your account has been credited profit for payout #".$sendMoney->invoice;
            BasicService::makeTransaction($user, getAmount($merchantCom),  0, '+', $trx_id, $remarks2 );




            $this->sendMailSms($user, 'PAYOUT_COMPLETE', [
                'amount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')),
                'currency' => $sendMoney->send_curr,
                'invoice' => $sendMoney->invoice
            ]);

            $msg = [
                'amount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')),
                'currency' => $sendMoney->send_curr
            ];
            $action = [
                "link" => '#',
                "icon" => "fas fa-money-bill-alt text-white"
            ];
            $this->userPushNotification($user, 'PAYOUT_COMPLETE', $msg, $action);

            session()->flash('success', ' Payout  has been Successful');
            return redirect()->route('user.transfer-log');

        }
        abort(404);
    }

    public function payoutHistory()
    {
        $page_title = 'Payout History';
        $sendMoneys = SendMoney::where('merchant_id', $this->user->id)->where('payment_status', 1)->orderBy('received_at', 'DESC')->paginate(config('basic.paginate'));
        return view($this->theme . 'operation.payoutHistory', compact('sendMoneys','page_title'));
    }

    public function verificationCheck()
    {
        // If we reach this method, it means all verifications are complete
        // (CheckUserStatus middleware would have redirected otherwise)
        return redirect()->route('home');
    }
}
