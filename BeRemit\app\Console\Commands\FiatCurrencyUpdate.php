<?php

namespace App\Console\Commands;

use App\Models\Configure;
use App\Models\Country;
use Facades\App\Services\BasicCurl;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class FiatCurrencyUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fiat-currency:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $basicControl = Configure::first();
        $countries = Country::select('id','code','rate')->where('status',1)->where('is_online_rate',1)->get()->makeHidden('flag');
        $endpoint = 'live';
        $access_key = $basicControl->currency_layer_access_key;;
        $currencies = join(',',$countries->pluck('code')->toArray()).','.config('basic.currency');

        $source  = "USD";
        $format  = 1;
        $url = 'http://api.currencylayer.com/'.$endpoint.'?access_key='.$access_key.'&currencies='.$currencies.'&source='.$source.'&format='.$format;
        $res = BasicCurl::curlGetRequest($url);
        $res = json_decode($res);


        if($res->success ==  true){
            $getRateCollect = collect($res->quotes)->toArray();
            foreach ($countries as $data){
                $newCode = $source.$data->code;
                if(isset($getRateCollect[$newCode])){
                    $data->rate = @$getRateCollect[$newCode];
                    $data->update();
                }
            }

            $configure = Configure::firstOrNew();
            if(isset($getRateCollect[$source.config('basic.currency')])){
                $baseRate = $getRateCollect[$source.config('basic.currency')];
                config(['basic.rate' => $baseRate]);
                $fp = fopen(base_path() . '/config/basic.php', 'w');
                fwrite($fp, '<?php return ' . var_export(config('basic'), true) . ';');
                fclose($fp);

                $configure->rate = $baseRate;
                $configure->save();
                $output = new \Symfony\Component\Console\Output\BufferedOutput();
                Artisan::call('optimize:clear', array(), $output);
                return $output->fetch();
            }

        }
        return 0;
    }
}
