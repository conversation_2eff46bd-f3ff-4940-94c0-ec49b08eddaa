@extends('admin.layouts.app')
@section('title')
    @lang('Basic Controls')
@endsection
@section('content')
    <div class="bd-callout bd-callout-warning m-0 m-md-4 my-4 m-md-0 ">
        <i class="fas fa-info-circle mr-2 text-info"></i> @lang("If you get 500(server error) for some reason, please turn on <b>Debug Mode</b> and try again. Then you can see what was missing in your system.") </div>
    <div class="card card-primary m-0 m-md-4 my-4 m-md-0">
        <div class="card-body">

            <form method="post" action="" novalidate="novalidate" class="needs-validation base-form">
                @csrf
                <div class="row">
                    <div class="form-group  col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">@lang('Site Title')</label>
                        <input type="text" name="site_title"
                               value="{{ old('site_title') ?? $control->site_title ?? 'Site Title' }}"
                               class="form-control ">

                        @error('site_title')
                        <span class="text-danger">{{ trans($message) }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">@lang('TimeZone')</label>
                        <select class="form-control" id="exampleFormControlSelect1" name="time_zone">
                            <option hidden>{{ old('time_zone', $control->time_zone)?? 'Select Time Zone' }}</option>
                            @foreach ($control->time_zone_all as $time_zone_local)
                                <option value="{{ $time_zone_local }}">@lang($time_zone_local)</option>
                            @endforeach
                        </select>

                        @error('time_zone')
                        <span class="text-danger">{{ trans($message) }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">@lang('Base Currency')</label>
                        <input type="text" name="currency" value="{{ old('currency') ?? $control->currency ?? 'USD' }}"
                               required="required" class="form-control currency_code">

                        @error('currency')
                        <span class="text-danger">{{ trans($message) }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">@lang('Currency Symbol')</label>
                        <input type="text" name="currency_symbol"
                               value="{{ old('currency_symbol') ?? $control->currency_symbol ?? '$' }}"
                               required="required" class="form-control ">

                        @error('currency_symbol')
                        <span class="text-danger">{{ trans($message) }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">{{trans('Rate')}}</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">
                                    {{trans('1')}} {{ trans('USD') }} =
                                </div>
                            </div>

                            <input type="text" class="form-control"
                                   name="rate"
                                   value="{{old('rate',getAmount($control->rate))}}"
                            >
                            <div class="input-group-append">
                                <div class="input-group-text set-currency">
                                </div>
                            </div>
                        </div>
                        @error('rate')
                        <span class="text-danger">{{ trans($message)  }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">@lang('Fraction number')</label>
                        <input type="text" name="fraction_number"
                               value="{{ old('fraction_number') ?? $control->fraction_number ?? '2' }}"
                               required="required" class="form-control ">
                        @error('fraction_number')
                        <span class="text-danger">{{ trans($message) }}</span>
                        @enderror
                    </div>


                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">@lang('Paginate Per Page')</label>
                        <input type="text" name="paginate" value="{{ old('paginate') ?? $control->paginate ?? '2' }}"
                               required="required" class="form-control ">
                        @error('paginate')
                        <span class="text-danger">{{ trans($message) }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">{{trans('Session Expired')}}</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-lg" value="{{ old('session_expire') ?? $control->session_expire ?? '' }}" name="session_expire">
                            <div class="input-group-append">
                                <span class="input-group-text">
                                    {{trans('Minutes')}}
                                </span>
                            </div>
                        </div>

                        @error('session_expire')
                        <span class="text-danger">{{ trans($message) }}</span>
                        @enderror
                    </div>



                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">{{trans('Merchant Commission')}} <small> ({{trans('Sending')}})</small></label>
                        <div class="input-group">
                            <input type="text" class="form-control"
                                   name="merchant_commission"
                                   value="{{old('merchant_commission',getAmount($control->merchant_commission))}}">
                            <div class="input-group-append">
                                <div class="input-group-text "> {{trans('%')}}
                                </div>
                            </div>
                        </div>

                        <span class="text-muted">{{trans('Merchant will get amount from sending Charge')}}</span>
                        @error('merchant_commission')
                        <span class="text-danger">{{ trans($message)  }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">{{trans('Merchant Commission')}} <small> ({{trans('Receiving')}})</small></label>
                        <div class="input-group">
                            <input type="text" class="form-control"
                                   name="merchant_profit"
                                   value="{{old('merchant_profit',getAmount($control->merchant_profit))}}">
                            <div class="input-group-append">
                                <div class="input-group-text "> {{trans('%')}}
                                </div>
                            </div>
                        </div>

                        <span class="text-muted">{{trans('Merchant will get amount from receiving Charge')}}</span>
                        @error('merchant_profit')
                        <span class="text-danger">{{ trans($message)  }}</span>
                        @enderror
                    </div>

                    <div class="col-12">
                        <h5 class="text-dark mb-3 mt-3 border-bottom pb-2">@lang('Agent Transfer Settings')</h5>
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">{{trans('Minimum Transfer Amount')}}</label>
                        <div class="input-group">
                            <input type="text" class="form-control"
                                   name="agent_transfer_min_amount"
                                   value="{{old('agent_transfer_min_amount',getAmount($control->agent_transfer_min_amount ?? 10))}}">
                            <div class="input-group-append">
                                <div class="input-group-text "> {{config('basic.currency')}}
                                </div>
                            </div>
                        </div>
                        @error('agent_transfer_min_amount')
                        <span class="text-danger">{{ trans($message)  }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">{{trans('Maximum Transfer Amount')}}</label>
                        <div class="input-group">
                            <input type="text" class="form-control"
                                   name="agent_transfer_max_amount"
                                   value="{{old('agent_transfer_max_amount',getAmount($control->agent_transfer_max_amount ?? 1000))}}">
                            <div class="input-group-append">
                                <div class="input-group-text "> {{config('basic.currency')}}
                                </div>
                            </div>
                        </div>
                        @error('agent_transfer_max_amount')
                        <span class="text-danger">{{ trans($message)  }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">{{trans('Fixed Fee')}}</label>
                        <div class="input-group">
                            <input type="text" class="form-control"
                                   name="agent_transfer_fixed_fee"
                                   value="{{old('agent_transfer_fixed_fee',getAmount($control->agent_transfer_fixed_fee ?? 1))}}">
                            <div class="input-group-append">
                                <div class="input-group-text "> {{config('basic.currency')}}
                                </div>
                            </div>
                        </div>
                        @error('agent_transfer_fixed_fee')
                        <span class="text-danger">{{ trans($message)  }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">{{trans('Percentage Fee')}}</label>
                        <div class="input-group">
                            <input type="text" class="form-control"
                                   name="agent_transfer_percentage_fee"
                                   value="{{old('agent_transfer_percentage_fee',getAmount($control->agent_transfer_percentage_fee ?? 1))}}">
                            <div class="input-group-append">
                                <div class="input-group-text "> {{trans('%')}}
                                </div>
                            </div>
                        </div>
                        @error('agent_transfer_percentage_fee')
                        <span class="text-danger">{{ trans($message)  }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">@lang('Strong Password')</label>
                        <div class="custom-switch-btn">
                            <input type='hidden' value='1' name='strong_password'>
                            <input type="checkbox" name="strong_password" class="custom-switch-checkbox"
                                   id="strong_password"
                                   value="0" {{($control->strong_password == 0) ? 'checked' : ''}} >
                            <label class="custom-switch-checkbox-label" for="strong_password">
                                <span class="custom-switch-checkbox-inner"></span>
                                <span class="custom-switch-checkbox-switch"></span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-3">
                        <label class="text-dark">@lang('Registration')</label>
                        <div class="custom-switch-btn">
                            <input type='hidden' value='1' name='registration'>
                            <input type="checkbox" name="registration" class="custom-switch-checkbox"
                                   id="registration"
                                   value="0" {{($control->registration == 0) ? 'checked' : ''}} >
                            <label class="custom-switch-checkbox-label" for="registration">
                                <span class="custom-switch-checkbox-inner"></span>
                                <span class="custom-switch-checkbox-switch"></span>
                            </label>
                        </div>
                    </div>


                    <div class="form-group col-lg-4 col-md-6">
                        <label class="d-block">@lang('Cron Set Up Pop Up')</label>
                        <div class="custom-switch-btn">
                            <input type='hidden' value='1' name='cron_set_up_pop_up'>
                            <input type="checkbox" name="cron_set_up_pop_up" class="custom-switch-checkbox"
                                   id="cron_set_up_pop_up"
                                   value="0" <?php if ($control->is_active_cron_notification == 0):echo 'checked'; endif ?> >
                            <label class="custom-switch-checkbox-label" for="cron_set_up_pop_up">
                                <span class="custom-switch-checkbox-inner"></span>
                                <span class="custom-switch-checkbox-switch"></span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group col-sm-2 col-lg-4">
                        <label class="font-weight-bold">@lang('Debug Mode')</label>
                        <div class="custom-switch-btn">
                            <input type='hidden' value='1' name='error_log'>
                            <input type="checkbox" name="error_log" class="custom-switch-checkbox"
                                   id="error_log"
                                   value="0" <?php if ($control->error_log == 0):echo 'checked'; endif ?> >
                            <label class="custom-switch-checkbox-label" for="error_log">
                                <span class="custom-switch-checkbox-inner"></span>
                                <span class="custom-switch-checkbox-switch"></span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group col-sm-6 col-md-4 col-lg-4">
                        <label class="d-block">@lang('Maintenance Mode')</label>
                        <div class="custom-switch-btn">
                            <input type='hidden' value='1' name='maintenance'>
                            <input type="checkbox" name="maintenance" class="custom-switch-checkbox"
                                   id="maintenance"
                                   value="0" {{($control->maintenance  == 0) ? 'checked' : ''}} >
                            <label class="custom-switch-checkbox-label" for="maintenance">
                                <span class="custom-switch-checkbox-inner"></span>
                                <span class="custom-switch-checkbox-switch"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn waves-effect waves-light btn-rounded btn-primary btn-block mt-3"><span><i class="fas fa-save pr-2"></i> @lang('Save Changes')</span></button>
            </form>
        </div>
    </div>





    <!-- Modal for Edit button -->
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content ">
                <div class="modal-header modal-colored-header bg-primary">
                    <h4 class="modal-title" id="myModalLabel">@lang('Currencylayer rate API')</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                    <p class="">{{trans('Get your rate API key from')}} <a href="https://currencylayer.com/">{{trans('currencylayer')}}</a></p>

                    <p class="text-dark"> {{trans('Set up this Cron job command on your server to get update rate')}}  <br>
                        <code> {{trans('curl -s')}} {{route('cron')}}</code></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@lang('Close')</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script>
        "use strict";
        $(document).ready(function () {
            $('select').select2({
                selectOnClose: true
            });

            setCurrency();
            $(document).on('change', '.currency_code', function (){
                setCurrency();
            });

            function setCurrency() {
                let currency = $('.currency_code').val();
                $('.set-currency').text(currency);
            }
        });
    </script>
@endpush
