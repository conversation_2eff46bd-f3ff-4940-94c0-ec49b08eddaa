/*==========================================*/
/*         MERCHANT DASHBOARD RTL STYLES   */
/*==========================================*/

/* RTL Layout Base Styles */
.rtl-layout {
    direction: rtl;
    text-align: right;
}

/* Sidebar RTL Adjustments */
.rtl-layout .left-sidebar {
    right: 0;
    left: auto;
}

.rtl-layout .page-wrapper {
    margin-right: 250px;
    margin-left: 0;
}

.rtl-layout .sidebar-nav ul li a {
    text-align: right;
}

.rtl-layout .sidebar-nav ul li a i {
    margin-left: 10px;
    margin-right: 0;
}

.rtl-layout .hide-menu {
    margin-right: 0;
    margin-left: 10px;
}

/* Header RTL Adjustments */
.rtl-layout .navbar-nav.float-right {
    float: left !important;
}

.rtl-layout .navbar-nav.float-left {
    float: right !important;
}

/* RTL Notification Bell Positioning - Ensure it stays visible and properly positioned */
.rtl-layout #pushNotificationArea {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
}

.rtl-layout .navbar-nav .nav-item#pushNotificationArea {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.rtl-layout #pushNotificationArea .nav-link {
    display: flex !important;
    align-items: center !important;
    visibility: visible !important;
    opacity: 1 !important;
    padding: 0.5rem 0.75rem !important;
}

.rtl-layout #pushNotificationArea [data-feather="bell"] {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 20px !important;
    height: 20px !important;
    stroke: currentColor !important;
    stroke-width: 2 !important;
    stroke-linecap: round !important;
    stroke-linejoin: round !important;
    fill: none !important;
}

.rtl-layout #pushNotificationArea .badge {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    transform: translate(50%, -50%) !important;
    z-index: 10 !important;
}

.rtl-layout .ml-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

.rtl-layout .mr-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

/* Fix for navbar collapse in RTL */
/*
.rtl-layout .navbar-collapse {
    direction: rtl;
    margin-right: 260px !important;
    margin-left: 0 !important;    
}
*/
@media (min-width: 768px) {
   

    #main-wrapper[data-layout=vertical][data-sidebar-position=fixed][data-sidebartype=full] .topbar .top-navbar .navbar-collapse {
        margin-right: 260px !important;
        margin-left: 0 !important;
    }

    #main-wrapper[data-layout=vertical][data-sidebar-position=fixed][data-sidebartype=mini-sidebar] .topbar .top-navbar .navbar-collapse,#main-wrapper[data-layout=vertical][data-sidebartype=mini-sidebar] .page-wrapper {
        margin-right: 65px !important
    }

    #main-wrapper[data-layout=vertical][data-sidebartype=mini-sidebar] .sidebar-nav ul .sidebar-item .sidebar-link .feather-icon,#main-wrapper[data-layout=vertical][data-sidebartype=mini-sidebar] .sidebar-nav ul .sidebar-item .sidebar-link i {
        margin-right: 7px !important;
        margin-left: 0 !important
    }


    #main-wrapper[data-layout=vertical][data-sidebartype=mini-sidebar] .left-sidebar:hover .sidebar-nav ul .sidebar-item .sidebar-link {
        margin-left: 17px
    }

    #main-wrapper[data-layout=vertical][data-sidebartype=mini-sidebar] .left-sidebar:hover .sidebar-nav ul .sidebar-item .sidebar-link .feather-icon,#main-wrapper[data-layout=vertical][data-sidebartype=mini-sidebar] .left-sidebar:hover .sidebar-nav ul .sidebar-item .sidebar-link i {
        margin-left: 8px;
        margin-right: 0
    }

}



/* Ensure navigation items are visible in RTL */
.rtl-layout .navbar-nav {
    direction: rtl;
}

.rtl-layout .navbar-nav .nav-item {
    direction: rtl;
}

/* Fix for notification area in RTL */
.rtl-layout #pushNotificationArea {
    direction: rtl;
}

.rtl-layout .navbar-nav.float-left.mr-auto.ml-3.pl-1 {
    float: right !important;
    margin-right: 0 !important;
    margin-left: auto !important;
    padding-left: 0 !important;
    padding-right: 1rem !important;
}

.rtl-layout .ml-1 { margin-left: 0 !important; margin-right: 0.25rem !important; }
.rtl-layout .ml-2 { margin-left: 0 !important; margin-right: 0.5rem !important; }
.rtl-layout .ml-3 { margin-left: 0 !important; margin-right: 1rem !important; }

.rtl-layout .mr-1 { margin-right: 0 !important; margin-left: 0.25rem !important; }
.rtl-layout .mr-2 { margin-right: 0 !important; margin-left: 0.5rem !important; }
.rtl-layout .mr-3 { margin-right: 0 !important; margin-left: 1rem !important; }

.rtl-layout .pl-1 { padding-left: 0 !important; padding-right: 0.25rem !important; }
.rtl-layout .pl-2 { padding-left: 0 !important; padding-right: 0.5rem !important; }
.rtl-layout .pl-3 { padding-left: 0 !important; padding-right: 1rem !important; }

.rtl-layout .pr-1 { padding-right: 0 !important; padding-left: 0.25rem !important; }
.rtl-layout .pr-2 { padding-right: 0 !important; padding-left: 0.5rem !important; }
.rtl-layout .pr-3 { padding-right: 0 !important; padding-left: 1rem !important; }

/* Dropdown RTL Adjustments */
.rtl-layout .dropdown-menu-right {
    right: auto !important;
    left: 0 !important;
}

.rtl-layout .dropdown-menu {
    text-align: right;
}

/* Fix for notification dropdown in RTL */
.rtl-layout .dropdown-menu-left {
    left: auto !important;
    right: 0 !important;
}

/* Ensure dropdowns are properly positioned */
.rtl-layout .navbar-nav .nav-item .dropdown-menu {
    left: auto !important;
    right: 0 !important;
    transform: none !important;
}

/* Fix for user dropdown positioning */
.rtl-layout .navbar-nav .nav-item:last-child .dropdown-menu {
    left: 0 !important;
    right: auto !important;
}

/* Card and Content RTL Adjustments */
.rtl-layout .card-body {
    text-align: right;
}

.rtl-layout .d-flex {
    direction: rtl;
}

.rtl-layout .text-truncate {
    text-align: right;
}

/* Form RTL Adjustments */
.rtl-layout .form-control {
    text-align: right;
}

.rtl-layout .form-group label {
    text-align: right;
}

.rtl-layout .input-group-prepend {
    order: 2;
}

.rtl-layout .input-group-append {
    order: 0;
}

/* Table RTL Adjustments */
.rtl-layout table {
    direction: rtl;
}

.rtl-layout th,
.rtl-layout td {
    text-align: right;
}

/* Button RTL Adjustments */
.rtl-layout .btn {
    text-align: center;
}

.rtl-layout .btn i {
    margin-left: 5px;
    margin-right: 0;
}

/* Breadcrumb RTL Adjustments */
.rtl-layout .breadcrumb {
    direction: rtl;
}

.rtl-layout .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    transform: scaleX(-1);
}

/* Navigation RTL Adjustments */
.rtl-layout .nav-toggler {
    right: auto;
    left: 15px;
}

/* Notification RTL Adjustments */
.rtl-layout .notify-no {
    right: 8px !important;
    left: auto !important;
    top: 8px !important;
    transform: translate(50%, -50%) !important;
}

/* Additional RTL notification badge positioning */
.rtl-layout #pushNotificationArea .badge.notify-no {
    position: absolute !important;
    right: 29px !important;
    left: auto !important;
    top: 38px !important;
    padding: 5px 2px;
}

/* Select2 RTL Adjustments */
.rtl-layout .select2-container--default .select2-selection--single {
    text-align: right;
}

.rtl-layout .select2-container--default .select2-selection--single .select2-selection__arrow {
    left: 1px;
    right: auto;
}

/* Badge RTL Adjustments */
.rtl-layout .badge {
    text-align: center;
}

/* Icon RTL Adjustments */
.rtl-layout .feather-icon,
.rtl-layout .svg-icon {
    margin-left: 5px;
    margin-right: 0;
}

/* Fix for feather icons in RTL */
.rtl-layout [data-feather] {
    display: inline-block !important;
    width: 1em;
    height: 1em;
    vertical-align: middle;
}

/* Fix for sidebar icons */
.rtl-layout .sidebar-nav ul li a i {
    margin-left: 10px;
    margin-right: 0;
    width: 20px;
    text-align: center;
}

/* Fix for FontAwesome icons in RTL */
.rtl-layout .fas,
.rtl-layout .far,
.rtl-layout .fab,
.rtl-layout .fal {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

/* Fix for notification bell icon */
.rtl-layout .navbar-nav .nav-link [data-feather="bell"] {
    display: inline-block !important;
}

/* Fix for language globe icon */
.rtl-layout .navbar-nav .nav-link [data-feather="globe"] {
    display: inline-block !important;
}

/* Fix for chevron icons */
.rtl-layout .navbar-nav .nav-link [data-feather="chevron-down"] {
    display: inline-block !important;
}

/* Ensure all SVG icons are visible */
.rtl-layout .svg-icon {
    display: inline-block !important;
    width: 1em;
    height: 1em;
    stroke-width: 2;
    stroke: currentColor;
    fill: none;
    stroke-linecap: round;
    stroke-linejoin: round;
}

/* Responsive RTL Adjustments */
@media (max-width: 767px) {
    .rtl-layout .page-wrapper {
        margin-right: 0;
        margin-left: 0;
    }
    
    .rtl-layout .left-sidebar {
        right: -250px;
        left: auto;
    }
    
    .rtl-layout .left-sidebar.show {
        right: 0;
    }
}

/* Custom RTL Fixes for Specific Elements */
.rtl-layout .width-40p {
    margin-left: 10px;
    margin-right: 0;
}

.rtl-layout .user-dd {
    right: auto !important;
    left: 0 !important;
}

.rtl-layout .animated.flipInY {
    animation-name: flipInY;
}

/* Fix for language dropdown positioning */
.rtl-layout .navbar-nav .dropdown-menu {
    right: 0;
    left: auto;
}

/* Arabic Font Support */
.rtl-layout {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Ensure proper text alignment for Arabic */
.rtl-layout h1, .rtl-layout h2, .rtl-layout h3, 
.rtl-layout h4, .rtl-layout h5, .rtl-layout h6 {
    text-align: right;
}

.rtl-layout p, .rtl-layout span, .rtl-layout div {
    text-align: right;
}

/* Fix for dashboard cards */
.rtl-layout .admin-fa_icon .card-body .d-flex {
    flex-direction: row-reverse;
}

.rtl-layout .admin-fa_icon .card-body .d-flex > div:first-child {
    margin-left: auto;
    margin-right: 0;
}

/* Fix for statistics cards alignment */
.rtl-layout .d-lg-flex.d-md-block.align-items-center {
    flex-direction: row-reverse;
}

.rtl-layout .mt-md-3.mt-lg-0 {
    margin-left: 0;
    margin-right: auto;
}

/* Additional sidebar fixes */
.rtl-layout .sidebar-nav ul li a .hide-menu {
    text-align: right;
}

.rtl-layout .left-sidebar .scroll-sidebar {
    direction: rtl;
}

/* Fix for main wrapper in RTL */
.rtl-layout #main-wrapper[data-sidebartype="full"] .page-wrapper {
    margin-right: 250px;
    margin-left: 0;
}

/* Fix for mobile sidebar */
@media (max-width: 767px) {
    .rtl-layout #main-wrapper[data-sidebartype="full"] .left-sidebar {
        right: -250px;
        left: auto;
    }

    .rtl-layout #main-wrapper[data-sidebartype="full"] .left-sidebar.show {
        right: 0;
    }
}

/* Fix for navbar brand */
.rtl-layout .navbar-brand {
    margin-left: 0;
    margin-right: 1rem;
}

/* Fix for topbar */
.rtl-layout .topbar .navbar-header {
    float: right;
}

/* Fix for page breadcrumb */
.rtl-layout .page-breadcrumb {
    text-align: right;
}

.rtl-layout .page-breadcrumb .breadcrumb {
    justify-content: flex-end;
}

/* Fix for card titles and content */
.rtl-layout .card-title {
    text-align: right;
}

.rtl-layout .text-muted {
    text-align: right;
}

/* Language dropdown specific fixes */
.rtl-layout .language-switch {
    text-align: right;
    direction: rtl;
}

.rtl-layout .language-switch i {
    margin-left: 5px;
    margin-right: 0;
}

.rtl-layout .language-switch .ml-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

/* Ensure language switcher is always visible */
.rtl-layout .navbar-nav .nav-item.dropdown {
    display: block !important;
    visibility: visible !important;
}

.rtl-layout .navbar-nav .nav-item.dropdown .nav-link {
    display: flex !important;
    align-items: center;
    visibility: visible !important;
}

/* Fix for language dropdown toggle */
.rtl-layout .navbar-nav .nav-item.dropdown .dropdown-toggle {
    display: flex !important;
    align-items: center;
}

/* Fix for language dropdown content */
.rtl-layout .navbar-nav .nav-item.dropdown .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: auto !important;
    right: 0 !important;
    z-index: 1000;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 0.875rem;
    color: #212529;
    text-align: right;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 0.25rem;
}

.rtl-layout .navbar-nav .nav-item.dropdown.show .dropdown-menu {
    display: block !important;
}

/* Notification and alert fixes */
.rtl-layout .alert {
    text-align: right;
}

.rtl-layout .toast {
    text-align: right;
}

/* Loading spinner fixes */
.rtl-layout .fa-spinner {
    margin-left: 5px;
    margin-right: 0;
}

/* Ensure proper font rendering for Arabic */
/*
.rtl-layout * {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}
*/

/* Fix for language dropdown positioning in RTL */
.rtl-layout .navbar-nav .nav-item .dropdown-menu {
    left: auto !important;
    right: 0 !important;
    transform: none !important;
}

/* Additional responsive fixes for RTL */
@media (max-width: 991px) {
    .rtl-layout .navbar-nav {
        text-align: right;
    }

    .rtl-layout .navbar-collapse {
        direction: rtl;
    }
}

@media (max-width: 767px) {
    html[dir="ltr"] .rtl-layout,
    html[dir="ltr"] body.rtl-layout,
    html[dir="ltr"] .rtl-layout * {
        direction: ltr !important;
        text-align: left !important;
    }
    html[dir="ltr"] .navbar-nav,
    html[dir="ltr"] .topbar {
        flex-direction: row !important;
    }
    /* Add more overrides as needed to neutralize RTL effects on mobile */
}
