<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Traits\Notify;
use App\Http\Traits\Upload;
use App\Models\Fund;
use App\Models\IdentifyForm;
use App\Models\KYC;
use App\Models\Language;
use App\Models\SendMoney;
use App\Models\Template;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use <PERSON><PERSON><PERSON>\Purify\Facades\Purify;
use PDF;

class AuthenticateUserController extends Controller
{

    use Notify, Upload;


    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth']);
        $this->middleware(function ($request, $next) {
            $this->user = auth()->user();
            return $next($request);
        });
    }


    public function checkValidCode($user, $code, $add_min = 10000)
    {
        if (!$code) return false;
        if (!$user->sent_at) return false;
        if (Carbon::parse($user->sent_at)->addMinutes($add_min) < Carbon::now()) return false;
        if ($user->verify_code !== $code) return false;
        return true;
    }

    public function sendMailCode()
    {
        $user = Auth()->user();
        if ($this->checkValidCode($user, $user->verify_code, 2)) {
            $target_time = Carbon::parse($user->sent_at)->addMinutes(2)->timestamp;
            $delay = $target_time - time();
            $delay = gmdate('i:s', $delay);

            $result['status'] = false;
            $result['message'] = 'Please Try after ' . $delay . ' Seconds';
            $result['data'] = [];
            return response($result, 200);
        }
        if (!$this->checkValidCode($user, $user->verify_code)) {
            $user->verify_code = code(6);
            $user->sent_at = Carbon::now();
            $user->save();
        } else {
            $user->sent_at = Carbon::now();
            $user->save();
        }


        $this->mail($user, 'VERIFICATION_CODE', [
            'code' => $user->verify_code
        ]);
        $result['status'] = true;
        $result['message'] = 'Email verification code sent successfully';
        $result['data'] = [];
        return response($result, 200);
    }

    public function mailVerify(Request $request)
    {
        $rules = ['email_verified_code' => 'required|max:6'];
        $message = ['email_verified_code.required' => 'Email verification code is required'];

        $validator = validator()->make($request->all(), $rules, $message);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }
        $user = auth()->user();
        if ($this->checkValidCode($user, $request->email_verified_code)) {
            $user->email_verification = 1;
            $user->verify_code = null;
            $user->sent_at = null;
            $user->save();


            $result['status'] = true;
            $result['message'] = 'Email Verification successful';
            $result['data'] = [];
            return response($result, 200);
        }


        $result['status'] = false;
        $result['message'] = 'Verification Code Did not matched';
        $result['data'] = [];
        return response($result, 200);
    }

    public function sendSmsCode()
    {
        $user = Auth()->user();
        if ($this->checkValidCode($user, $user->verify_code, 2)) {
            $target_time = Carbon::parse($user->sent_at)->addMinutes(2)->timestamp;
            $delay = $target_time - time();
            $delay = gmdate('i:s', $delay);

            $result['status'] = false;
            $result['message'] = 'Please Try after ' . $delay . ' Seconds';
            $result['data'] = [];
            return response($result, 200);
        }

        if (!$this->checkValidCode($user, $user->verify_code)) {
            $user->verify_code = code(6);
            $user->sent_at = Carbon::now();
            $user->save();
        } else {
            $user->sent_at = Carbon::now();
            $user->save();
        }

        $this->sms($user, 'VERIFICATION_CODE', [
            'code' => $user->verify_code
        ]);

        $result['status'] = true;
        $result['message'] = 'SMS verification code sent successfully';
        $result['data'] = [];
        return response($result, 200);

    }

    public function smsVerify(Request $request)
    {
        $rules = ['sms_verified_code' => 'required|max:6'];
        $message = ['sms_verified_code.required' => 'SMS verification code is required'];
        $validator = validator()->make($request->all(), $rules, $message);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }
        $user = auth()->user();
        if ($this->checkValidCode($user, $request->sms_verified_code)) {
            $user->sms_verification = 1;
            $user->verify_code = null;
            $user->sent_at = null;
            $user->save();


            $result['status'] = true;
            $result['message'] = 'Mobile Verification successful';
            $result['data'] = [];
            return response($result, 200);
        }

        $result['status'] = false;
        $result['message'] = 'Verification Code Did not matched';
        $result['data'] = [];
        return response($result, 200);
    }

    public function user()
    {
        $result['status'] = true;
        $result['message'] = null;
        $result['data'] = [
            'user' => auth()->user(),
            'languages' => Language::all()
        ];
        return response($result, 200);
    }


    public function updateProfile(Request $request)
    {
        $rules = ['image' => 'required'];
        $validator = validator()->make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }
        $user = auth()->user();
        $filename = $user->image ?? strRandom(10);

        $path = config('location.user.path');
        if ($request->has('image')) {
            $image = $request->image;
            $oldFile = $path . $user->image;
            $img = $image;
            $image_parts = explode(";base64,", $img);

            $image_type_aux = explode("image/", $image_parts[0]);
            $image_base64 = base64_decode($image_parts[0]);
            $filename = uniqid() . '.jpg';
            $file = $path . $filename;
            if (file_exists($oldFile)) {
                @unlink($oldFile);
            }
            @file_put_contents($file, $image_base64);
        }


        $user->image = $filename;
        $user->save();

        $result['status'] = true;
        $result['message'] = 'Profile Updated successfully.';
        $result['data'] = [
            'user' => auth()->user()
        ];
        return response($result, 200);
    }

    public function updateInformation(Request $request)
    {
        $user = auth()->user();
        $languages = Language::all()->map(function ($item) {
            return $item->id;
        });
        $validator = validator()->make($request->all(), [
            'firstname' => 'required',
            'lastname' => 'required',
            'username' => "sometimes|required|alpha_dash|min:4|unique:users,username," . $user->id,
            'address' => 'required',
//            'language_id' => ['sometimes',Rule::in($languages)],
        ], [
            'firstname.required' => 'First Name field is required',
            'lastname.required' => 'Last Name field is required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }

        $req = Purify::clean($request->all());

        if ($request->has('image')) {
            $rules = ['image' => 'required'];
            $validator = validator()->make($request->all(), $rules);
            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()]);
            }
            $user = auth()->user();
            //$filename = $user->image??strRandom(10);

            $path = config('location.user.path');
            $size = config('location.user.size');

            $image = $request->image;
            $oldFile = $user->image;
            $image = $this->uploadImage($image, $path, $size, $oldFile);

            $user->image = $image;
            $user->save();
        }

        if ($request->has('language_id')) {
            $user->language_id = $req['language_id'];
        }
        $user->firstname = $req['firstname'];
        $user->lastname = $req['lastname'];
        $user->username = $req['username'];
        $user->address = $req['address'];
        $user->save();

        $result['status'] = true;
        $result['message'] = 'Updated successfully.';
        $result['data'] = [
            'user' => auth()->user()
        ];
        return response($result, 200);
    }

    public function changePassword(Request $request)
    {
        $rules = [
            'current_password' => 'required',
            'password' => 'required|min:5|confirmed'
        ];
        $validator = validator()->make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }

        try {
            $user = Auth::user();
            if (Hash::check($request->current_password, $user->password)) {
                $user->password = Hash::make($request->password);
                $user->save();

                $result['status'] = true;
                $result['message'] = 'Password has been changed.';
                $result['data'] = [];
                return response($result, 200);
            } else {
                $result['status'] = false;
                $result['message'] = 'Current password not match';
                $result['data'] = [];
                return response($result, 200);
            }

        } catch (\PDOException $e) {
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            $result['data'] = [];
            return response($result, 200);
        }
    }


    public function transaction()
    {
        $transactions = Transaction::where('user_id', Auth::id())->orderBy('id', 'DESC')->paginate(config('basic.paginate'));
        $transactions = resourcePaginate($transactions, function ($data, $key) use ($transactions) {
            return [
                'SL' => loopIndex($transactions) + $key,
                'Transaction_ID' => $data->trx_id,
                'Transaction_Type' => $data->trx_type,
                'Amount' => getAmount($data->amount, config('basic.fraction_number')) . ' ' . trans(config('basic.currency')),
                'Charge' => getAmount($data->charge, config('basic.fraction_number')) . ' ' . trans(config('basic.currency')),
                'Remark' => $data->remarks,
                'Time' => dateTime($data->created_at),
            ];
        });

        $result['status'] = true;
        $result['message'] = null;
        $result['data'] = $transactions;
        return response($result, 200);
    }


    public function transactionSearch(Request $request)
    {
        $search = $request->all();
        $dateSearch = $request->datetrx;
        $date = preg_match("/^[0-9]{2,4}\-[0-9]{1,2}\-[0-9]{1,2}$/", $dateSearch);
        $transaction = Transaction::where('user_id', Auth::id())->with('user')
            ->when(@$search['transaction_id'], function ($query) use ($search) {
                return $query->where('trx_id', 'LIKE', "%{$search['transaction_id']}%");
            })
            ->when(@$search['remark'], function ($query) use ($search) {
                return $query->where('remarks', 'LIKE', "%{$search['remark']}%");
            })
            ->when($date == 1, function ($query) use ($dateSearch) {
                return $query->whereDate("created_at", $dateSearch);
            })
            ->paginate(config('basic.paginate'));
        $transactions = $transaction->appends($search);

        $transactions = resourcePaginate($transactions, function ($data, $key) use ($transactions) {
            return [
                'SL' => loopIndex($transactions) + $key,
                'Transaction_ID' => $data->trx_id,
                'Transaction_Type' => $data->trx_type,
                'Amount' => getAmount($data->amount, config('basic.fraction_number')) . ' ' . trans(config('basic.currency')),
                'Charge' => getAmount($data->charge, config('basic.fraction_number')) . ' ' . trans(config('basic.currency')),
                'Remark' => $data->remarks,
                'Time' => dateTime($data->created_at),
            ];
        });


        $result['status'] = true;
        $result['message'] = null;
        $result['data'] = $transactions;
        return response($result, 200);
    }


    public function fundHistory()
    {
        $funds = Fund::where('user_id', Auth::id())->where('status', '!=', 0)->orderBy('id', 'DESC')->with('gateway:id,name')->paginate(config('basic.paginate'));
        $funds = resourcePaginate($funds, function ($data, $key) use ($funds) {
            $status = '';
            if ($data->status == 1) {
                $status = 'Complete';
            } elseif ($data->status == 2) {
                $status = 'Pending';
            } elseif ($data->status == 3) {
                $status = 'Cancel';
            }
            return [
                'SL' => loopIndex($funds) + $key,
                'Gateway' => optional($data->gateway)->name,
                'Transaction_ID' => $data->transaction,
                'Amount' => getAmount($data->amount, config('basic.fraction_number')) . ' ' . trans(config('basic.currency')),
                'Charge' => getAmount($data->charge, config('basic.fraction_number')) . ' ' . trans(config('basic.currency')),
                'Status' => $status,
                'Time' => dateTime($data->created_at, 'd M Y h:i A'),
            ];
        });

        $result['status'] = true;
        $result['message'] = null;
        $result['data'] = $funds;
        return response($result, 200);
    }

    public function fundHistorySearch(Request $request)
    {
        $search = $request->all();
        $dateSearch = $request->date_time;
        $date = preg_match("/^[0-9]{2,4}\-[0-9]{1,2}\-[0-9]{1,2}$/", $dateSearch);

        $funds = Fund::orderBy('id', 'DESC')->where('user_id', Auth::id())->where('status', '!=', 0)
            ->when(isset($search['name']), function ($query) use ($search) {
                return $query->where('transaction', 'LIKE', $search['name']);
            })
            ->when($date == 1, function ($query) use ($dateSearch) {
                return $query->whereDate("created_at", $dateSearch);
            })
            ->when(isset($search['status']), function ($query) use ($search) {
                return $query->where('status', $search['status']);
            })
            ->with('gateway:id,name')
            ->paginate(config('basic.paginate'));
        $funds->appends($search);


        $funds = resourcePaginate($funds, function ($data, $key) use ($funds) {
            $status = '';
            if ($data->status == 1) {
                $status = 'Complete';
            } elseif ($data->status == 2) {
                $status = 'Pending';
            } elseif ($data->status == 3) {
                $status = 'Cancel';
            }
            return [
                'SL' => loopIndex($funds) + $key,
                'Gateway' => optional($data->gateway)->name,
                'Transaction_ID' => $data->transaction,
                'Amount' => getAmount($data->amount, config('basic.fraction_number')) . ' ' . trans(config('basic.currency')),
                'Charge' => getAmount($data->charge, config('basic.fraction_number')) . ' ' . trans(config('basic.currency')),
                'Status' => $status,
                'Time' => dateTime($data->created_at, 'd M Y h:i A'),
            ];
        });

        $result['status'] = true;
        $result['message'] = null;
        $result['data'] = $funds;
        return response($result, 200);
    }


    public function transferLog(Request $request)
    {
        $user = auth()->user();
        $search = $request->all();
        $dateSearch = $request->date_time;
        $date = preg_match("/^[0-9]{2,4}\-[0-9]{1,2}\-[0-9]{1,2}$/", $dateSearch);

        $sendMoneys = SendMoney::where('user_id', $user->id)->latest()
            ->when(isset($search['invoice']), function ($query) use ($search) {
                return $query->where('invoice', 'LIKE', $search['invoice']);
            })
            ->when(isset($search['send_amount']), function ($query) use ($search) {
                return $query->where('send_amount', $search['send_amount']);
            })
            ->when(isset($search['receive_amount']), function ($query) use ($search) {
                return $query->where('recipient_get_amount', $search['receive_amount']);
            })
            ->when($date == 1, function ($query) use ($dateSearch) {
                return $query->whereDate("paid_at", $dateSearch);
            })
            ->paginate(config('basic.paginate'));

        $sendMoneys = resourcePaginate($sendMoneys, function ($data, $key) use ($sendMoneys) {
            $status = '';
            if ($data->status == 0 && $data->payment_status == 0) {
                $status = 'Information Need';
            } elseif ($data->status == 2 && $data->payment_status == 0) {
                $status = 'Please Pay';
            } elseif ($data->status == 3 || $data->payment_status == 2) {
                $status = 'Cancelled';
            } elseif ($data->status == 1 && $data->payment_status == 1) {
                $status = 'Completed';
            } elseif ($data->status == 2 && $data->payment_status == 1) {
                $status = 'Processing';
            } elseif ($data->status == 2 && $data->payment_status == 3) {
                $status = 'Payment Hold';
            }

            $route = [];
            if ($data->status == 0 && $data->payment_status == 0) {
                $route['fill_up_form'] = route('transfer-form', [$data->invoice]);
            } elseif ($data->status == 2 && $data->payment_status == 0) {
                $route['Details'] = route('transferInfo', [$data->invoice]);
            } elseif ($data->status != 0) {
                $route['Details'] = route('transferInfo', [$data->invoice]);
            } else {
                $route = [];
            }
            return [
                'SL' => loopIndex($sendMoneys) + $key,
                'Invoice' => $data->invoice,
                'Recipient' => ($data->recipient_name) ?? 'N/A',
                'Send_Amount' => getAmount($data->totalPay, config('basic.fraction_number')) . ' ' . trans($data->send_curr),
                'Send_At' => ($data->paid_at) ? dateTime($data->paid_at) : 'N/A',
                'Receive_Amount' => getAmount($data->recipient_get_amount, config('basic.fraction_number')) . ' ' . trans($data->receive_curr),
                'Receive_At' => ($data->received_at) ? dateTime($data->received_at) : 'N/A',
                'Rate' => "1 $data->send_curr = " . getAmount($data->rate, config('basic.fraction_number')) . ' ' . trans($data->receive_curr),
                'Status' => $status,
            ];
        });

        $result['status'] = true;
        $result['message'] = null;
        $result['data'] = $sendMoneys;
        return response($result, 200);
    }


    public function transferInfo($invoice)
    {
        $user = auth()->user();
        $sendMoney = SendMoney::where('user_id', $user->id)->where('invoice', $invoice)->first();

//        $sendMoney = SendMoney::where('invoice',$invoice)->first();
        if (!$sendMoney) {
            $result['status'] = false;
            $result['message'] = 'Invalid Transaction';
            $result['data'] = [];
            return response($result, 200);
        }

        $templateSection = ['contact-us'];
        $contactUs = Template::templateMedia()->whereIn('section_name', $templateSection)->get()->groupBy('section_name');
        $contactUs = $contactUs['contact-us'][0];
        $request = app('request');

        $data['contact'] = [
            'email' => @$contactUs->description->email,
            'phone' => @$contactUs->description->phone,
            'address' => @$contactUs->description->address,
            'site_title' => config('basic.site_title'),
            'Host' =>$request->getHost(),
        ];
        $status = '';
        if ($sendMoney->status == 0 && $sendMoney->payment_status == 0) {
            $status = 'Information Need';
        } elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 0) {
            $status = 'Sender Not Pay Yet';
        } elseif ($sendMoney->status == 3 || $sendMoney->payment_status == 2) {
            $status = 'Cancelled';
        } elseif ($sendMoney->status == 1 && $sendMoney->payment_status == 1) {
            $status = 'Completed';
        } elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 1) {
            $status = 'Processing';
        } elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 3) {
            $status = 'Payment Hold';
        }

        $data['invoice'] = [
            'Transaction' => $sendMoney->invoice,
            'Status' => $status,
            'TransactionDate' => ($sendMoney->paid_at) ? dateTime($sendMoney->paid_at) : dateTime($sendMoney->created_at),
            'Service' => optional($sendMoney->service)->name,
            'ServiceProvider' => optional($sendMoney->provider)->name,
            'SendAmount' => getAmount($sendMoney->send_amount, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
            'Fees' => getAmount($sendMoney->fees, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
            'discountYes' => $sendMoney->discount,
            'Discount' => getAmount($sendMoney->discount, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
            'TotalSendAmount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')) . ' ' . $sendMoney->send_curr,
            'RecipientAmount' => getAmount($sendMoney->recipient_get_amount, config('basic.fraction_number')) . ' ' . $sendMoney->receive_curr,
            'Rate' => '1 ' . $sendMoney->send_curr . ' = ' . getAmount($sendMoney->rate, config('basic.fraction_number')) . ' ' . $sendMoney->receive_curr,

            'Sender' => [
                'Name' => optional($sendMoney->user)->fullname,
                'Phone' => optional($sendMoney->user)->phone,
                'Address' => optional($sendMoney->user)->address,
            ],
            'FundingSource' => $sendMoney->fund_source,
            'SendingPurpose' => $sendMoney->purpose,

            'Recipient' => [
                'Name' => $sendMoney->recipient_name,
                'Email' => $sendMoney->recipient_email,
                'Phone' => $sendMoney->recipient_contact_no,
            ]
        ];

        $result['status'] = true;
        $result['data'] = $data;
        return response($result, 200);

        // Use our custom PDF service for better Arabic support
        $pdf = \App\Services\PdfService::generatePdf('themes.minimal.layouts.invoice', $data, 'invoice.pdf');
        return $pdf;
    }


    public function identityVerifyClue()
    {
        $user = auth()->user();

        $result['status'] = true;
        if ($user->identity_verify == 3) {
            $result['message'] = 'You previous request has been rejected';
        } elseif ($user->identity_verify == 1) {
            $result['message'] = 'Your KYC submission has been pending';
        } elseif ($user->identity_verify == 2) {
            $result['message'] = 'Your KYC already verified';
        } else {
            $result['message'] = null;
        }

        if (in_array($user->identity_verify, [0, 3])) {
            $result['data'] = IdentifyForm::where('status', 1)->get();
        } else {
            $result['data'] = [];
        }
        return response($result, 200);
    }

    public function identityVerify(Request $request)
    {

        $validator = validator()->make($request->all(), [
            'identity_type' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }

        $identityFormList = IdentifyForm::where('status', 1)->get();
        $rules['identity_type'] = ["required", Rule::in($identityFormList->pluck('slug')->toArray())];
        $identity_type = $request->identity_type;
        $identityForm = IdentifyForm::where('slug', trim($identity_type))->where('status', 1)->firstOrFail();

        $params = $identityForm->services_form;

        $rules = [];
        $inputField = [];
        $verifyImages = [];

        if ($params != null) {
            foreach ($params as $key => $cus) {
                $rules[$key] = [$cus->validation];
                if ($cus->type == 'file') {
//                    array_push($rules[$key], 'image');
//                    array_push($rules[$key], 'mimes:jpeg,jpg,png');
//                    array_push($rules[$key], 'max:2048');
//                    array_push($verifyImages, $key);
                    array_push($rules[$key], trim($cus->validation));
//                    array_push($rules[$key], 'max:' . trim($cus->field_length));

                }
                if ($cus->type == 'text') {
//                    array_push($rules[$key], 'max:191');
                    array_push($rules[$key], trim($cus->validation));
                    if ($cus->length_type == 'max') {
                        array_push($rules[$key], 'max:' . trim($cus->field_length));
                    } elseif ($cus->length_type == 'digits') {
                        array_push($rules[$key], 'digits:' . trim($cus->field_length));
                    }

                }
                if ($cus->type == 'textarea') {
//                    array_push($rules[$key], 'max:300');
                    array_push($rules[$key], trim($cus->validation));
                    array_push($rules[$key], 'max:' . trim($cus->field_length));
                }
                $inputField[] = $key;
            }
        }

        $validator = validator()->make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }


        $path = config('location.kyc.path') . date('Y') . '/' . date('m') . '/' . date('d') . '/';
        $this->makeDirectory($path);
        $collection = collect($request);

        $reqField = [];
        if ($params != null) {
            foreach ($collection as $k => $v) {
                foreach ($params as $inKey => $inVal) {
                    if ($k != $inKey) {
                        continue;
                    } else {
                        if ($inVal->type == 'file') {
                            if ($request->has($inKey)) {
//                                try {
                                $img = $_POST[$inKey];
                                $image_parts = explode(";base64,", $img);
                                $image_type_aux = explode("image/", $image_parts[0]);
                                $image_type = $image_type_aux[1];
                                $image_base64 = base64_decode($image_parts[1]);

                                $filename = uniqid() . '.jpg';
                                $file = $path . $filename;
                                @file_put_contents($file, $image_base64);
                                $reqField[$inKey] = [
                                    'field_name' => $filename,
                                    'file_location' => $path,
                                    'type' => $inVal->type,
                                ];
//                                } catch (\Exception $exp) {
//                                    $result['status'] = false;
//                                    $result['message'] =  'Could not upload your ' . $inKey;
//                                    $result['data'] = [];
//                                    return response($result, 200);
//                                }
                            }
                        } else {
                            $reqField[$inKey] = $v;
                            $reqField[$inKey] = [
                                'field_name' => $v,
                                'type' => $inVal->type,
                            ];
                        }
                    }
                }
            }
        }

        try {

            DB::beginTransaction();

            $user = $this->user;
            $kyc = new KYC();
            $kyc->user_id = $user->id;
            $kyc->kyc_type = $identityForm->slug;
            $kyc->details = $reqField;
            $kyc->save();

            $user->identity_verify = 1;
            $user->save();

            if (!$kyc) {
                DB::rollBack();
                $result['status'] = false;
                $result['message'] = 'Failed to submit request';
                $result['data'] = [];
                return response($result, 200);
            }
            DB::commit();

            $result['status'] = true;
            $result['message'] = 'KYC request has been submitted.';
            $result['data'] = [];
            return response($result, 200);

        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            $result['data'] = [];
            return response($result, 200);
        }
    }


    public function addressVerifyClue()
    {
        $user = auth()->user();

        $result['status'] = true;
        if ($user->address_verify == 3) {
            $result['message'] = 'You previous request has been rejected';
        } elseif ($user->address_verify == 1) {
            $result['message'] = 'Your KYC submission has been pending';
        } elseif ($user->address_verify == 2) {
            $result['message'] = 'Your KYC already verified';
        } else {
            $result['message'] = null;
        }

        if (in_array($user->address_verify, [0, 3])) {
            $result['data'] = [
                "addressProof" => [
                    "field_name" => "addressProof",
                    "field_level" => "Address Proof",
                    "type" => "file",
                    "field_length" => "2500",
                    "length_type" => "max",
                    "validation" => "required"
                ]
            ];
        } else {
            $result['data'] = [];
        }
        return response($result, 200);
    }

    public function addressVerify(Request $request)
    {
        $rules = [];
        $rules['addressProof'] = ['required'];
        $validator = validator()->make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }

        $path = config('location.kyc.path') . date('Y') . '/' . date('m') . '/' . date('d') . '/';

        $this->makeDirectory($path);

        $reqField = [];
//        try {
        if ($request->has('addressProof')) {

            $img = $_POST['addressProof'];
            $image_parts = explode(";base64,", $img);
            $image_type_aux = explode("image/", $image_parts[0]);
            $image_type = $image_type_aux[1];
            $image_base64 = base64_decode($image_parts[1]);

            $filename = uniqid() . '.jpg';
            $file = $path . $filename;
            @file_put_contents($file, $image_base64);
            $reqField['addressProof'] = [
                'field_name' => $filename,
                'file_location' => $path,
                'type' => 'file',
            ];
        } else {
            $result['status'] = false;
            $result['message'] = 'Please select a ' . 'address Proof';
            $result['data'] = [];
            return response($result, 200);
        }
//        } catch (\Exception $exp) {
//            $result['status'] = false;
//            $result['message'] = $exp->getMessage();;
////            $result['message'] = 'Could not upload your ' . 'address Proof';
//            $result['data'] = [];
//            return response($result, 200);
//        }


        try {

            DB::beginTransaction();
            $user = $this->user;
            $kyc = new KYC();
            $kyc->user_id = $user->id;
            $kyc->kyc_type = 'address-verification';
            $kyc->details = $reqField;
            $kyc->save();
            $user->address_verify = 1;
            $user->save();

            if (!$kyc) {
                DB::rollBack();
                $result['status'] = false;
                $result['message'] = 'Failed to submit request';
                $result['data'] = [];
                return response($result, 200);
            }
            DB::commit();

            $result['status'] = true;
            $result['message'] = 'Your request has been submitted.';
            $result['data'] = [];
            return response($result, 200);

        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            $result['data'] = [];
            return response($result, 200);
        }
    }


    public function transferLogDelete(Request $request)
    {
        $sendMoney = SendMoney::withTrashed()->where('user_id', auth()->id())->where('invoice', $request->invoice)->first();
        if ($sendMoney->payment_status == 0) {
            $sendMoney->forceDelete();
            $result['status'] = true;
            $result['message'] = 'Transaction Has Been Removed.';
            return response($result, 200);
        }
        $result['status'] = false;
        $result['message'] = 'Unable to remove Transaction';
        $result['data'] = [];
        return response($result, 200);
    }

}
