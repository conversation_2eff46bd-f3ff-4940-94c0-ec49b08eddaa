net\authorize\api\contract\v1\SubsequentAuthInformationType:
    properties:
        originalNetworkTransId:
            expose: true
            access_type: public_method
            serialized_name: originalNetworkTransId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getOriginalNetworkTransId
                setter: setOriginalNetworkTransId
            type: string
        reason:
            expose: true
            access_type: public_method
            serialized_name: reason
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getReason
                setter: setReason
            type: string
