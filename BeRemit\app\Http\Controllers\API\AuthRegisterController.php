<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Traits\Notify;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use <PERSON><PERSON><PERSON>\Purify\Facades\Purify;

class AuthRegisterController extends Controller
{
    use Notify;

    public function register(Request $request)
    {
        $basic = basicControl();
        $sponsor = session()->get('sponsor');
        if ($sponsor != null) {
            $sponsorId = User::where('username', $sponsor)->first();
        } else {
            $sponsorId = null;
        }

        $country_code = "AF,AL,DZ,AS,AD,AO,AI,AG,AR,AM,AW,AU,AZ,BH,BD,BB,BY,BE,BZ,BJ,BM,BT,BO,BA,BW,BR,IO,VG,BN,BG,BF,MM,BI,KH,CM,CA,CV,KY,CF,ID,CL,CN,CO,KM,CK,CR,CI,HR,CU,CY,CZ,CD,DK,DJ,DM,DO,EC,EG,SV,GQ,ER,EE,ET,FK,FO,FM,FJ,FI,FR,GF,PF,GA,GE,DE,GH,GI,GR,GL,GD,GP,GU,GT,GN,GW,GY,HT,HN,HK,HU,IS,IN,IR,IQ,IE,IL,IT,JM,JP,JO,KZ,KE,KI,XK,KW,KG,LA,LV,LB,LS,LR,LY,LI,LT,LU,MO,MK,MG,MW,MY,MV,ML,MT,MH,MQ,MR,MU,YT,MX,MD,MC,MN,ME,MS,MA,MZ,NA,NR,NP,NL,AN,NC,NZ,NI,NE,NG,NU,NF,KP,MP,NO,OM,PK,PW,PS,PA,PG,PY,PE,PH,PL,PT,PR,QA,CG,RE,RO,RU,RW,BL,SH,KN,MF,PM,VC,WS,SM,ST,SA,SN,RS,SC,SL,SG,SK,SI,SB,SO,ZA,KR,ES,LK,LC,SD,SR,SZ,SE,CH,SY,TW,TJ,TZ,TH,BS,GM,TL,TG,TK,TO,TT,TN,TR,TM,TC,TV,UG,UA,AE,GB,US,UY,VI,UZ,VU,VA,VE,VN,WF,YE,ZM,ZW";
        $rules['firstname'] = ['required', 'string', 'max:91'];
        $rules['lastname'] = ['required', 'string', 'max:91'];
        $rules['username'] = ['required', 'alpha_dash', 'min:5', 'unique:users,username'];
        $rules['email'] = ['required', 'string', 'email', 'max:255', 'unique:users,email'];
        $rules['phone'] = ['required', 'phone:' . $country_code];
        if (config('basic.strong_password') == 0) {
            $rules['password'] = ['required', 'min:5', 'confirmed'];
        } else {
            $rules['password'] = ["required", 'confirmed',
                Password::min(6)->mixedCase()->letters()->numbers()->symbols()->uncompromised()];
        }

        $validator = validator()->make($request->all(), $rules, [
            'firstname.required' => 'First Name Field is required',
            'lastname.required' => 'Last Name Field is required',
            'phone' => 'The :attribute field contains an invalid number.'
        ]);

        if ($validator->fails()) {
            return response(['errors' => $validator->errors()->all()], 422);
        }


        // Sanitize user input
        $sanitizedData = Purify::clean($request->all());

        $user = User::create([
            'firstname' => $sanitizedData['firstname'],
            'lastname' => $sanitizedData['lastname'],
            'username' => trim(strtolower($sanitizedData['username'])),
            'email' => strtolower(trim($sanitizedData['email'])),
            'referral_id' => ($sponsorId != null) ? $sponsorId->id : null,
            'password' => Hash::make($sanitizedData['password']),
            'phone' => $sanitizedData['phone'],
            'status' => 1,
            'email_verification' => $basic->email_verification ? 0 : 1,
            'sms_verification' => $basic->sms_verification ? 0 : 1,
            'last_login' => Carbon::now(),
            'last_login_ip' => request()->ip(),
        ]);


        $token = $user->createToken('Laravel Password Grant Client')->accessToken;


        $this->adminPushNotification('ADDED_USER', [
            'username' => $user->username,
        ], [
            "link" => route('admin.user-edit', $user->id),
            "icon" => "fas fa-user text-white"
        ]);


        $response = ['token' => $token, 'user' => $user];
        $result['status'] = true;
        $result['message'] = 'Registration Successfully';
        $result['data'] = $response;
        return response($result, 200);

    }
}
