@extends($theme.'layouts.user')
@section('title')
    {{ 'Pay with '.optional($order->gateway)->name ?? '' }}
@endsection

@section('content')


    <section id="add-recipient-form" class="wow fadeInUp" data-wow-delay=".2s" data-wow-offset="300">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="card secbg text-center">
                        <div class="card-body d-flex flex-wrap text-center align-items-center">

                            <div>
                                <img
                                    src="{{getFile(config('location.gateway.path').optional($order->gateway)->image)}}"
                                    class="img-thumbnail mx-auto w-75" alt="..">
                            </div>

                            <div>
                                <h4>@lang('Please Pay') {{round($order->final_amount)}} {{$order->gateway_currency}}</h4>

                                <button type="button"
                                        class="btn btn-success mt-3"
                                        id="pay-button">@lang('Pay Now')
                                </button>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    @push('script')
        <script type="text/javascript"
                src="https://app.sandbox.midtrans.com/snap/snap.js"
                data-client-key="{{ $data->client_key }}"></script>

        <script defer>
            var payButton = document.getElementById('pay-button');
            payButton.addEventListener('click', function () {
                window.snap.pay("{{ $data->token }}");
            });
        </script>
    @endpush



@endsection
