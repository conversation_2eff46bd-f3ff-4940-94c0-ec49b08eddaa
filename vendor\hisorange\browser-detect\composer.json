{"name": "hisorange/browser-detect", "description": "Browser & Mobile detection package for Laravel.", "keywords": ["laravel", "user-agent", "browser", "mobile", "detect", "tablet", "user agent", "mobile", "tablet", "analyse", "php", "hisorange"], "homepage": "https://github.com/hisorange/browser-detect", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON> (hisorange)", "email": "<EMAIL>"}], "repositories": [{"type": "vcs", "url": "https://github.com/mustangostang/spyc"}], "require": {"php": "^7.2 || ^8.0", "ua-parser/uap-php": "~3.9", "league/pipeline": "^1.0", "mobiledetect/mobiledetectlib": "~2.8", "jaybizzle/crawler-detect": "~1.2", "matomo/device-detector": "^4.0 || ^5.0 || ^6.0"}, "require-dev": {"phpunit/phpunit": "~5.0 || ~6.0 || ~7.0 || ~8.0 || ~9.0", "orchestra/testbench": "~4.0 || ~5.0 || ~6.0 || ~7.0", "php-coveralls/php-coveralls": "~1.0 || ~2.0", "squizlabs/php_codesniffer": "^3.5", "phpstan/phpstan": "^0.12.11"}, "autoload": {"psr-4": {"hisorange\\BrowserDetect\\": "src/"}}, "autoload-dev": {"psr-4": {"hisorange\\BrowserDetect\\Test\\": "tests/"}}, "minimum-stability": "stable", "extra": {"laravel": {"providers": ["hisorange\\BrowserDetect\\ServiceProvider"], "aliases": {"Browser": "hisorange\\BrowserDetect\\Facade"}}}, "scripts": {"test-dev": "phpunit --coverage-text", "test": "phpunit --coverage-clover ./tests/logs/clover.xml"}}