<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        '*ajax*',
        '*active',
        'admin/service',
        '*sort-payment-methods',
        '*add-fund',
        '*pay-now*',
        'success',
        'failed',
        'payment/*',
        '*checkContact',
        "*push-chat-newMessage",
        '*withdraw-bank-list',
        '*withdraw-bank-from',
    ];
}
