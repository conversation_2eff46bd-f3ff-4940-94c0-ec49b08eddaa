<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AgentTransfer;
use App\Models\User;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON>\Purify\Facades\Purify;

class AgentTransferController extends Controller
{
    public function index()
    {
        $page_title = "Agent Transfers";
        $transfers = AgentTransfer::with(['sender', 'receiver'])
            ->latest()
            ->paginate(config('basic.paginate'));
        return view('admin.agent_transfer.index', compact('page_title', 'transfers'));
    }

    public function search(Request $request)
    {
        $search = $request->all();
        $dateSearch = $request->date_time;
        $date = preg_match("/^[0-9]{2,4}\-[0-9]{1,2}\-[0-9]{1,2}$/", $dateSearch);

        $transfers = AgentTransfer::with(['sender', 'receiver'])
            ->when(isset($search['sender']), function ($query) use ($search) {
                return $query->whereHas('sender', function ($q) use ($search) {
                    $q->where('username', 'LIKE', "%{$search['sender']}%")
                        ->orWhere('email', 'LIKE', "%{$search['sender']}%");
                });
            })
            ->when(isset($search['receiver']), function ($query) use ($search) {
                return $query->whereHas('receiver', function ($q) use ($search) {
                    $q->where('username', 'LIKE', "%{$search['receiver']}%")
                        ->orWhere('email', 'LIKE', "%{$search['receiver']}%");
                });
            })
            ->when(isset($search['trx_id']), function ($query) use ($search) {
                return $query->where('trx_id', 'LIKE', "%{$search['trx_id']}%");
            })
            ->when($date == 1, function ($query) use ($dateSearch) {
                return $query->whereDate("created_at", $dateSearch);
            })
            ->when(isset($search['status']), function ($query) use ($search) {
                return $query->where('status', $search['status']);
            })
            ->latest()
            ->paginate(config('basic.paginate'));
        $page_title = "Search Agent Transfers";
        return view('admin.agent_transfer.index', compact('page_title', 'transfers'));
    }

    public function details($id)
    {
        $transfer = AgentTransfer::with(['sender', 'receiver'])->findOrFail($id);
        $page_title = "Agent Transfer Details";
        return view('admin.agent_transfer.details', compact('page_title', 'transfer'));
    }
}
