<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterRowsToTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add payment_id to funds table for generic payment tracking
        Schema::table('funds', function (Blueprint $table) {
            $table->string('payment_id')->nullable();
        });

        // Add merchant flag to users table for user type differentiation
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('merchant')->default(0)->after('balance');
        });

        // Add default status to languages table for multi-language support
        Schema::table('languages', function (Blueprint $table) {
            $table->boolean('default_status')->default(0)->after('is_active');
        });

        // Add generic configuration options to configures table
        Schema::table('configures', function (Blueprint $table) {
            $table->boolean('maintenance')->default(0);
            $table->string('tawk_id')->nullable();
            $table->boolean('tawk_status')->default(0);
            $table->boolean('fb_messenger_status')->default(0);
            $table->string('fb_app_id')->nullable();
            $table->string('fb_page_id')->nullable();
            $table->boolean('reCaptcha_status_login')->default(0);
            $table->boolean('reCaptcha_status_registration')->default(0);
            $table->string('MEASUREMENT_ID')->nullable();
            $table->boolean('analytic_status')->default(0);
            $table->boolean('error_log')->default(0);
            $table->boolean('is_active_cron_notification')->default(0);
            $table->integer('session_expire')->default(10);
        });


        Schema::create('payout_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('code')->nullable();
            $table->text('description')->nullable();
            $table->text('bank_name')->nullable();
            $table->text('banks')->nullable();
            $table->text('parameters')->nullable();
            $table->text('extra_parameters')->nullable();
            $table->string('image')->nullable();
            $table->decimal('minimum_amount')->nullable();
            $table->decimal('maximum_amount')->nullable();
            $table->decimal('fixed_charge')->nullable();
            $table->decimal('percent_charge')->nullable();
            $table->boolean('status')->default(1);
            $table->text('input_form')->nullable();
            $table->text('currency_lists')->nullable();
            $table->text('supported_currency')->nullable();
            $table->text('convert_rate')->nullable();
            $table->boolean('is_automatic')->default(0);
            $table->boolean('is_sandbox')->default(0);
            $table->boolean('is_auto_update')->default(1);
            $table->boolean('environment')->default(1)->comment('0=>text, 1=>live');
            $table->text('duration')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });

        Schema::create('payouts', function (Blueprint $table) {
            $table->id();
            $table->integer('admin_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('payout_method_id')->nullable();
            $table->string('response_id')->nullable();
            $table->string('currency_code')->nullable();
            $table->decimal('percentage', 18, 8)->default(0)->comment('Percent of charge');
            $table->decimal('charge_percentage', 18, 8)->default(0)->comment('After adding percent of charge');
            $table->decimal('charge_fixed', 18, 8)->default(0);
            $table->decimal('charge', 18, 8)->default(0);
            $table->decimal('amount', 18, 8)->default(0);
            $table->decimal('net_amount', 18, 8)->nullable();
            $table->decimal('transfer_amount', 18, 8)->default(0)->comment('Amount deduct from sender');
            $table->decimal('received_amount', 18, 8)->default(0)->comment('Amount add to receiver');
            $table->boolean('charge_from')->default(0)->comment('0 = Sender, 1 = Receiver');
            $table->text('note')->nullable();
            $table->text('withdraw_information')->nullable();
            $table->text('meta_field')->nullable()->comment('for flutterwave');
            $table->string('email')->nullable();
            $table->boolean('status')->default(0)->comment('0=Pending, 1=generate, 2 = payment done, 5 = cancel,6=failed');
            $table->string('utr')->nullable();
            $table->text('last_error')->nullable()->comment('api error');
            $table->string('trx_id')->nullable();
            $table->string('balance_type')->nullable();
            $table->text('feedback')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });

        Schema::create('top_up_logs', function (Blueprint $table) {
            $table->id();
            $table->integer('send_money_id')->nullable();
            $table->integer('customIdentifier')->nullable();
            $table->text('topup_response')->nullable();
            $table->boolean('status')->default(0);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });



        Schema::table('colors', function (Blueprint $table) {
            $table->string('base_alternative_color',20)->nullable()->default('#4db377')->after('border_color');
            $table->string('button_highlighter_color',20)->nullable()->default('#4db377');
            $table->string('hero_calculator_bg',20)->nullable()->default('#2c4e5d');
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('configures', function (Blueprint $table) {
            $table->dropColumn('merchant_commission');
            $table->dropColumn('merchant_profit');
            $table->dropColumn('maintenance');
            $table->dropColumn('tawk_id');
            $table->dropColumn('tawk_status');
            $table->dropColumn('fb_messenger_status');
            $table->dropColumn('fb_app_id');
            $table->dropColumn('fb_page_id');
            $table->dropColumn('reCaptcha_status_login');
            $table->dropColumn('reCaptcha_status_registration');
            $table->dropColumn('MEASUREMENT_ID');
            $table->dropColumn('analytic_status');
            $table->dropColumn('error_log');
            $table->dropColumn('is_active_cron_notification');
            $table->dropColumn('currency_layer_access_key');
            $table->dropColumn('currency_layer_auto_update');
            $table->dropColumn('currency_layer_auto_update_at');
            $table->dropColumn('coin_market_cap_app_key');
            $table->dropColumn('coin_market_cap_auto_update');
            $table->dropColumn('coin_market_cap_auto_update_at');
            $table->dropColumn('session_expire');
            $table->dropColumn('client_id');
            $table->dropColumn('client_secret');
            $table->dropColumn('sandbox');
            $table->dropColumn('PUBLIC_KEY');
            $table->dropColumn('SECRET_KEY');
            $table->dropColumn('ENCRYPTION_KEY');
        });

        Schema::table('countries', function (Blueprint $table) {
            $table->dropColumn('iso_code');
            $table->dropColumn('maximum_amount');
        });

        Schema::table('country_services', function (Blueprint $table) {
            $table->dropColumn('bank_code');
            $table->dropColumn('operatorId');
            $table->dropColumn('localMinAmount');
            $table->dropColumn('localMaxAmount');
        });

        Schema::table('funds', function (Blueprint $table) {
            $table->dropColumn('payment_id');
        });

        Schema::table('send_money', function (Blueprint $table) {
            $table->dropColumn('sender_name');
            $table->dropColumn('sender_phone');
            $table->dropColumn('sender_email');
            $table->dropColumn('sender_address');
            $table->dropColumn('sender_city');
            $table->dropColumn('sender_post_code');
            $table->dropColumn('sender_state');
            $table->dropColumn('sender_country');
            $table->dropColumn('sender_identity_verification');
            $table->dropColumn('sender_identity_type');
            $table->dropColumn('sender_address_verification');
            $table->dropColumn('merchant_id');
            $table->dropColumn('merchant_commission');
            $table->dropColumn('merchant_profit');
            $table->dropColumn('admin_commission');
            $table->dropColumn('payment_type');
            $table->dropColumn('flutter_status');
            $table->dropColumn('flutter_response');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('merchant');
        });

        Schema::table('languages', function (Blueprint $table) {
            $table->dropColumn('default_status');
        });
        Schema::dropIfExists('payout_methods');

        Schema::dropIfExists('payouts');

        Schema::dropIfExists('top_up_logs');


        Schema::table('colors', function (Blueprint $table) {
            $table->dropColumn('base_alternative_color');
            $table->dropColumn('button_highlighter_color');
            $table->dropColumn('hero_calculator_bg');
        });

    }
}
