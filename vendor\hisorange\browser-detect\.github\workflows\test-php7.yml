name: PHP7

# Manual run
on: workflow_dispatch

jobs:
  matrix-build:
    # Ubuntu latest will be ubuntu-20.04 which doesn't support php 7.3
    runs-on: ubuntu-18.04

    strategy:
      fail-fast: false
      matrix:
        php-version: [7.3]
        laravel-version:
          [
            6.2,
            6.4,
            6.5,
            6.8,
            6.12,
            6.18,
            6.19,
            6.20.0,
            7.0,
            7.3,
            7.6,
            7.12,
            7.25,
            7.28,
            7.29,
            7.30.1,
            8.0,
            8.12,
            8.13,
            8.14,
            8.15,
            8.16,
            8.17,
            8.18,
            8.19,
            8.20.1,
            8.21,
            8.22,
            8.23,
            8.24,
            8.25,
            8.26,
            8.27,
            8.28,
          ]

    steps:
      - uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@master
        with:
          php-version: ${{ matrix.php-version }}

      - name: Composer update
        run: composer self-update >/dev/null 2>&1

      - name: Lock laravel/framework version
        env:
          LARAVEL_VERSION: ${{ matrix.laravel-version }}
        run: composer require laravel/framework:${{ matrix.laravel-version }} --no-update

      - name: Vendor update
        run: composer update --prefer-source --no-interaction

      - name: Run test suites
        run: composer run-script test

      - name: Analyze
        run: vendor/bin/phpstan analyse -c phpstan.neon ./src/

      # - name: phpcs
      #   run: php vendor/bin/phpcs --standard=PSR12 ./src/
