{"name": "laravel/socialite", "description": "Laravel wrapper around OAuth 1 & OAuth 2 libraries.", "keywords": ["o<PERSON>h", "laravel"], "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/socialite/issues", "source": "https://github.com/laravel/socialite"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.2|^8.0", "ext-json": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "illuminate/contracts": "^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/http": "^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0", "league/oauth1-client": "^1.10.1"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^4.0|^5.0|^6.0|^7.0|^8.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.0|^9.3"}, "autoload": {"psr-4": {"Laravel\\Socialite\\": "src/"}}, "autoload-dev": {"psr-4": {"Laravel\\Socialite\\Tests\\": "tests/"}}, "extra": {"branch-alias": {"dev-master": "5.x-dev"}, "laravel": {"providers": ["Laravel\\Socialite\\SocialiteServiceProvider"], "aliases": {"Socialite": "Laravel\\Socialite\\Facades\\Socialite"}}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}