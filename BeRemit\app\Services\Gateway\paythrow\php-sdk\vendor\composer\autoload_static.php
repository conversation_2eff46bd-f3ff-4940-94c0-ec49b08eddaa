<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitad394a258cc9921b0a5590315c794bb2
{
    public static $prefixesPsr0 = array (
        'P' => 
        array (
            'PayThrow' => 
            array (
                0 => __DIR__ . '/../..' . '/src',
            ),
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixesPsr0 = ComposerStaticInitad394a258cc9921b0a5590315c794bb2::$prefixesPsr0;
            $loader->classMap = ComposerStaticInitad394a258cc9921b0a5590315c794bb2::$classMap;

        }, null, ClassLoader::class);
    }
}
