@extends('admin.layouts.app')
@section('title')
    @lang('Topup Reloadly Settings')
@endsection
@section('content')

    <div class="row">
        <div class="col-md-6">
            <div class="card card-primary m-0 m-md-4 my-4 m-md-0 shadow">
                <div class="card-body">
                    <form method="post" action="{{ route('admin.topup-reloadly.update') }}"
                          class="needs-validation base-form">
                        @csrf

                        <div class="row my-3">

                            <div class="form-group col-sm-12  col-12">
                                <label class="text-dark">@lang('Reloadly Client Id')</label>
                                <input type="text" name="client_id"
                                       value="{{ old('client_id') ?? $control->client_id ?? '' }}"
                                       required="required" class="form-control ">
                                @error('client_id')
                                <span class="text-danger">{{ trans($message) }}</span>
                                @enderror
                            </div>


                            <div class="form-group col-sm-12  col-12">
                                <label class="text-dark">@lang('Reloadly Client Secret')</label>
                                <input type="text" name="client_secret"
                                       value="{{ old('client_secret') ?? $control->client_secret ?? '' }}"
                                       required="required" class="form-control ">
                                @error('client_secret')
                                <span class="text-danger">{{ trans($message) }}</span>
                                @enderror
                            </div>


                            <div class="form-group col-sm-12 col-md-6 col-12">
                                <label class="d-block">@lang('Reloadly Sandbox Mode')</label>
                                <div class="custom-switch-btn">
                                    <input type='hidden' value='1' name='sandbox'>
                                    <input type="checkbox" name="sandbox" class="custom-switch-checkbox"
                                           id="sandbox"
                                           value="0" {{($control->sandbox  == 0) ? 'checked' : ''}} >
                                    <label class="custom-switch-checkbox-label" for="sandbox">
                                        <span class="custom-switch-checkbox-inner"></span>
                                        <span class="custom-switch-checkbox-switch"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="form-group col-sm-12 col-md-6 col-12">
                                <label class="d-block">@lang('Reloadly Service')</label>
                                <div class="custom-switch-btn">
                                    <input type='hidden' value='1' name='reloadly_service'>
                                    <input type="checkbox" name="reloadly_service" class="custom-switch-checkbox"
                                           id="reloadly_service"
                                           value="0" {{($control->reloadly_service  == 0) ? 'checked' : ''}} >
                                    <label class="custom-switch-checkbox-label" for="reloadly_service">
                                        <span class="custom-switch-checkbox-inner2"></span>
                                        <span class="custom-switch-checkbox-switch"></span>
                                    </label>
                                </div>
                            </div>

                        </div>

                        <button type="submit"
                                class="btn waves-effect waves-light btn-rounded btn-primary btn-block mt-3"><span><i
                                    class="fas fa-save pr-2"></i> @lang('Save Changes')</span></button>
                    </form>
                </div>
            </div>

        </div>


        <div class="col-md-6">
            <div class="card card-primary m-0 m-md-4 my-4 m-md-0 shadow">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between mb-3">
                        <div class="col-md-12">
                            <h5 class="card-title  font-weight-bold color-primary">@lang('Reloadly  Automated Airtime By Admin')</h5>
                        </div>
                    </div>

                    <p>
                        The fastest, easiest API to top-up Airtime globally.
                        After getting payments from users/customers, you can send topup/airtime automatically to recipients from your account.

                        Send Airtime with our local and international Top-up API to billions worldwide. Get access to a network of 800+ operators and allow your customers to recharge in over 170 countries.
                    </p>
                    <p>
                        Recharge Data Bundles Packages with the Reloadly Top-up API. Access a network of hundreds of mobile operators like MTN, Airtel, Vodafone and many more.

                    </p>


                </div>
            </div>
        </div>
    </div>

@endsection

