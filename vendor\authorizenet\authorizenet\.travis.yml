language: php

sudo: false

dist: trusty

env:
    - TEST_SUITE=samples

php:
    - 5.6
    - 7.0
    - 7.1

before_install:
    # execute all of the commands which need to be executed 
    # before installing dependencies
    - composer validate # make sure that our composer.json file is valid for packaging

install:
    # install all of the dependencies we need here
    - pecl install xmldiff
    - composer install --prefer-dist

before_script:
    # execute all of the commands which need to be executed 
    # before running actual tests
    - git submodule update --remote --recursive

script:
    # execute all of the tests or other commands to determine
    # whether the build will pass or fail
    - if [[ "$TEST_SUITE" == "samples" ]]; then phpenv config-rm xdebug.ini; cp -R lib sample-code-php/; cp -R vendor sample-code-php/; cd sample-code-php; vendor/phpunit/phpunit/phpunit test-runner.php .;  fi

after_script:
#    - if [[ "$TEST_SUITE" == "coverage" ]]; then wget https://scrutinizer-ci.com/ocular.phar; fi
#    - if [[ "$TEST_SUITE" == "coverage" ]]; then php ocular.phar code-coverage:upload --format=php-clover coverage.clover; fi
