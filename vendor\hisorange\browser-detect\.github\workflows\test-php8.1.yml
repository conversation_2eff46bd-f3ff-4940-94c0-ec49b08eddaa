name: PHP8.1

# Manual run
on: workflow_dispatch

jobs:
  matrix-build:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        php-version: [8.1]
        # these versions supports php8 in their composer.json
        laravel-version:
          [
            9.0,
            9.1,
            9.2,
            9.3,
            9.4,
            9.5,
            9.6,
            9.7,
            9.8,
          ]

    steps:
      - uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@master
        with:
          php-version: ${{ matrix.php-version }}

      - name: Composer update
        run: composer self-update >/dev/null 2>&1

      - name: Lock laravel/framework version
        env:
          LARAVEL_VERSION: ${{ matrix.laravel-version }}
        run: composer require laravel/framework:${{ matrix.laravel-version }} --no-update

      - name: Vendor update
        run: composer update --prefer-source --no-interaction

      - name: Run test suites
        run: composer run-script test

      - name: Analyze
        run: vendor/bin/phpstan analyse -c phpstan.neon ./src/

      # - name: phpcs
      #   run: php vendor/bin/phpcs --standard=PSR12 ./src/
