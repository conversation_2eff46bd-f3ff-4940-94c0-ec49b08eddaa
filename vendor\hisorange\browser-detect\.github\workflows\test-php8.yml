name: PHP8

# Manual run
on: workflow_dispatch

jobs:
  matrix-build:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        php-version: [8.0]
        # these versions supports php8 in their composer.json
        laravel-version:
          [
            6.20.0,
            7.29,
            7.30.1,
            8.12,
            8.13,
            8.14,
            8.15,
            8.16,
            8.17,
            8.18,
            8.19,
            8.20.1,
            8.21,
            8.22,
            8.23,
            8.24,
            8.25,
            8.26,
            8.27,
            8.28,
          ]

    steps:
      - uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@master
        with:
          php-version: ${{ matrix.php-version }}

      - name: Composer update
        run: composer self-update >/dev/null 2>&1

      - name: Lock laravel/framework version
        env:
          LARAVEL_VERSION: ${{ matrix.laravel-version }}
        run: composer require laravel/framework:${{ matrix.laravel-version }} --no-update

      - name: Vendor update
        run: composer update --prefer-source --no-interaction

      - name: Run test suites
        run: composer run-script test

      - name: Analyze
        run: vendor/bin/phpstan analyse -c phpstan.neon ./src/

      # - name: phpcs
      #   run: php vendor/bin/phpcs --standard=PSR12 ./src/
