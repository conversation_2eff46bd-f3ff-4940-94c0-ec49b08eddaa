<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:anet="AnetApi/xml/v1/schema/AnetApiSchema.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="AnetApi/xml/v1/schema/AnetApiSchema.xsd" elementFormDefault="qualified">
  <!-- 
  xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   Request type definitions begin here
  xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  -->
  <xs:simpleType name="numericString">
    <xs:restriction base="xs:string">
      <xs:pattern value="[0-9]+"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="alphaNumericString">
    <xs:restriction base="xs:string">
      <xs:pattern value="[0-9a-zA-Z]+"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfLong">
    <xs:sequence>
      <xs:element name="long" type="xs:long" nillable="false" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfNumericString">
    <xs:sequence>
      <xs:element name="numericString" type="anet:numericString" nillable="false" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfString">
    <xs:sequence>
      <xs:element name="string" type="xs:string" nillable="false" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfLineItem">
    <xs:sequence>
      <xs:element name="lineItem" type="anet:lineItemType" nillable="false" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfBatchStatisticType">
    <xs:sequence>
      <xs:element name="statistic" type="anet:batchStatisticType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfBatchDetailsType">
    <xs:sequence>
      <xs:element name="batch" type="anet:batchDetailsType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfTransactionSummaryType">
    <xs:sequence>
      <xs:element name="transaction" type="anet:transactionSummaryType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ARBTransactionList">
    <xs:sequence>
      <xs:element name="arbTransaction" type="anet:arbTransaction" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfSetting">
    <xs:sequence>
      <xs:element name="setting" type="anet:settingType" nillable="false" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfFDSFilter">
    <xs:sequence>
      <xs:element name="FDSFilter" type="anet:FDSFilterType" nillable="false" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfPermissionType">
    <xs:sequence>
      <xs:element name="permission" type="anet:permissionType" nillable="false" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:simpleType name="bankAccountTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="checking"/>
      <xs:enumeration value="savings"/>
      <xs:enumeration value="businessChecking"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="echeckTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="PPD"/>
      <xs:enumeration value="WEB"/>
      <xs:enumeration value="CCD"/>
      <xs:enumeration value="TEL"/>
      <xs:enumeration value="ARC"/>
      <xs:enumeration value="BOC"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="paymentMethodEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="creditCard"/>
      <xs:enumeration value="eCheck"/>
      <xs:enumeration value="payPal"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="cardTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Visa"/>
      <xs:enumeration value="MasterCard"/>
      <xs:enumeration value="AmericanExpress"/>
      <xs:enumeration value="Discover"/>
      <xs:enumeration value="JCB"/>
      <xs:enumeration value="DinersClub"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="accountTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Visa"/>
      <xs:enumeration value="MasterCard"/>
      <xs:enumeration value="AmericanExpress"/>
      <xs:enumeration value="Discover"/>
      <xs:enumeration value="JCB"/>
      <xs:enumeration value="DinersClub"/>
      <xs:enumeration value="eCheck"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="customerTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="individual"/>
      <xs:enumeration value="business"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="ARBSubscriptionUnitEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="days"/>
      <xs:enumeration value="months"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="validationModeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="none"/>
      <xs:enumeration value="testMode"/>
      <xs:enumeration value="liveMode"/>
      <xs:enumeration value="oldLiveMode"/>
      <!-- Free test-mode transaction. No validation against live cardholder account. -->
      <!-- Validate against live cardholder account for 0.00 if available, 0.01 otherwise. -->
      <!-- Validate against live cardholder account for 0.01 even if 0.00 option is available. NOT RECOMMENDED. Use of this option can result in fines from your processor. -->
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="splitTenderStatusEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="completed"/>
      <xs:enumeration value="held"/>
      <xs:enumeration value="voided"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="ARBSubscriptionStatusEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="active"/>
      <xs:enumeration value="expired"/>
      <xs:enumeration value="suspended"/>
      <xs:enumeration value="canceled"/>
      <xs:enumeration value="terminated"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="transactionTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="authOnlyTransaction"/>
      <xs:enumeration value="authCaptureTransaction"/>
      <xs:enumeration value="captureOnlyTransaction"/>
      <xs:enumeration value="refundTransaction"/>
      <xs:enumeration value="priorAuthCaptureTransaction"/>
      <xs:enumeration value="voidTransaction"/>
      <xs:enumeration value="getDetailsTransaction"/>
      <xs:enumeration value="authOnlyContinueTransaction"/>
      <xs:enumeration value="authCaptureContinueTransaction"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="transactionStatusEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="authorizedPendingCapture"/>
      <xs:enumeration value="capturedPendingSettlement"/>
      <xs:enumeration value="communicationError"/>
      <xs:enumeration value="refundSettledSuccessfully"/>
      <xs:enumeration value="refundPendingSettlement"/>
      <xs:enumeration value="approvedReview"/>
      <xs:enumeration value="declined"/>
      <xs:enumeration value="couldNotVoid"/>
      <xs:enumeration value="expired"/>
      <xs:enumeration value="generalError"/>
      <xs:enumeration value="pendingFinalSettlement"/>
      <xs:enumeration value="pendingSettlement"/>
      <xs:enumeration value="failedReview"/>
      <xs:enumeration value="settledSuccessfully"/>
      <xs:enumeration value="settlementError"/>
      <xs:enumeration value="underReview"/>
      <xs:enumeration value="updatingSettlement"/>
      <xs:enumeration value="voided"/>
      <xs:enumeration value="FDSPendingReview"/>
      <xs:enumeration value="FDSAuthorizedPendingReview"/>
      <xs:enumeration value="returnedItem"/>
      <xs:enumeration value="chargeback"/>
      <xs:enumeration value="chargebackReversal"/>
      <xs:enumeration value="authorizedPendingRelease"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="settlementStateEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="settledSuccessfully"/>
      <xs:enumeration value="settlementError"/>
      <xs:enumeration value="pendingSettlement"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="FDSFilterActionEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="reject"/>
      <xs:enumeration value="decline"/>
      <xs:enumeration value="hold"/>
      <xs:enumeration value="authAndHold"/>
      <xs:enumeration value="report"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="permissionsEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="API_Merchant_BasicReporting"/>
      <xs:enumeration value="Submit_Charge"/>
      <xs:enumeration value="Submit_Refund"/>
      <xs:enumeration value="Submit_Update"/>
      <xs:enumeration value="Mobile_Admin"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="deviceActivationEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Activate"/>
      <xs:enumeration value="Disable"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="TransactionGroupStatusEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="any"/>
      <xs:enumeration value="pendingApproval"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="afdsTransactionEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="approve"/>
      <xs:enumeration value="decline"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:simpleType name="customerProfileTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="regular" />
      <xs:enumeration value="guest" />
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->

  <xs:complexType name="driversLicenseType">
    <xs:sequence>
      <!-- Format of number should be string or four X's followed by the last four digits. -->
      <xs:element name="number">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="5"/>
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="state">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="2"/>
            <xs:maxLength value="2"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <!-- Format of dateOfBirth should be xs:date (1965-01-28) or XX/XX/1965. -->
      <xs:element name="dateOfBirth">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="8"/>
            <xs:maxLength value="10"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="driversLicenseMaskedType">
    <xs:sequence>
      <xs:element name="number">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:length value="8"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="state">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="2"/>
            <xs:maxLength value="2"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="dateOfBirth">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="8"/>
            <xs:maxLength value="10"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="nameAndAddressType">
    <xs:sequence>
      <xs:element name="firstName" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="50"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="lastName" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="50"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="company" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="50"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="address" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="60"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="city" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="state" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="zip" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="country" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="60"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="merchantContactType">
    <xs:sequence>
      <xs:element name="merchantName" type="xs:string" minOccurs="0"/>
      <xs:element name="merchantAddress" type="xs:string" minOccurs="0"/>
      <xs:element name="merchantCity" type="xs:string" minOccurs="0"/>
      <xs:element name="merchantState" type="xs:string" minOccurs="0"/>
      <xs:element name="merchantZip" type="xs:string" minOccurs="0"/>
      <xs:element name="merchantPhone" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="transRetailInfoType">
    <xs:sequence>
      <xs:element name="marketType" type="xs:string" default="2" minOccurs="0"/>
      <xs:element name="deviceType" type="xs:string" minOccurs="0"/>
      <xs:element name="customerSignature" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="terminalNumber" type="xs:string" minOccurs="0" maxOccurs="1"/>      
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="creditCardSimpleType">
    <xs:sequence>
      <!-- Format of cardNumber should be numeric string or four X's followed by the last four digits. -->
      <xs:element name="cardNumber">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="4"/>
            <xs:maxLength value="16"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <!-- Format of expirationDate should be gYearMonth (such as 2001-10) or four X's. -->
      <xs:element name="expirationDate">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="4"/>
            <xs:maxLength value="7"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="creditCardTrackType">
    <xs:choice>
      <xs:element name="track1" type="xs:string"/>
      <xs:element name="track2" type="xs:string"/>
    </xs:choice>
  </xs:complexType>
  <xs:simpleType name="cardCode">
    <xs:restriction base="anet:numericString">
      <xs:minLength value="3"/>
      <xs:maxLength value="4"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:complexType name="creditCardType">
    <xs:complexContent>
      <xs:extension base="anet:creditCardSimpleType">
        <xs:sequence>
          <!-- cardCode may be passed in for validation but it will not be stored. -->
          <xs:element name="cardCode" type="anet:cardCode" minOccurs="0" maxOccurs="1"/>
          <!-- To identify whether the CardNumber passed in is a PaymentToken or a real creditCardNumber. -->
          <xs:element name="isPaymentToken" type="xs:boolean" minOccurs="0"/>
          <!-- If the CardNumber passed in is a paymentToken, a cryptogram is needed for one-off payments. -->
          <xs:element name="cryptogram" type="xs:string" minOccurs="0"/>
          <!-- This is only needed for chase pay. -->
          <xs:element name="tokenRequestorName" type="xs:string" minOccurs="0" maxOccurs="1"/>		         
	        <!-- This is only needed for chase pay. -->
          <xs:element name="tokenRequestorId" type="xs:string" minOccurs="0" maxOccurs="1"/>
          <!-- This is only needed for chase pay. -->
          <xs:element name="tokenRequestorEci" type="xs:string" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="creditCardMaskedType">
    <xs:sequence>
      <xs:element name="cardNumber">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:length value="8"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="expirationDate">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="4"/>
            <xs:maxLength value="7"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="cardType" type="xs:string" minOccurs="0"/>
      <!-- anet:cardTypeEnum -->
      <xs:element name="cardArt" type="anet:cardArt" minOccurs="0"/>
      <xs:element name="issuerNumber" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:length value="6"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="isPaymentToken" type="xs:boolean" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ccAuthenticationType">
    <xs:sequence>
      <xs:element name="authenticationIndicator" type="xs:string"/>
      <xs:element name="cardholderAuthenticationValue" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="bankAccountType">
    <xs:sequence>
      <xs:element name="accountType" type="anet:bankAccountTypeEnum" minOccurs="0"/>
      <!-- Format of routingNumber should be nine digits or four X's followed by the last four digits. -->
      <xs:element name="routingNumber">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="9"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <!-- Format of accountNumber should be numeric string or four X's followed by the last four digits. -->
      <xs:element name="accountNumber">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="17"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="nameOnAccount">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="22"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="echeckType" type="anet:echeckTypeEnum" minOccurs="0"/>
      <xs:element name="bankName" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="50"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="checkNumber" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="15"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="bankAccountMaskedType">
    <xs:sequence>
      <xs:element name="accountType" type="anet:bankAccountTypeEnum" minOccurs="0"/>
      <xs:element name="routingNumber">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:length value="8"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="accountNumber">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:length value="8"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="nameOnAccount">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="22"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="echeckType" type="anet:echeckTypeEnum" minOccurs="0"/>
      <xs:element name="bankName" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="50"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!--============= opaqueDataType ======================================================-->
  <xs:complexType name="opaqueDataType">
    <xs:sequence>
      <xs:element name="dataDescriptor" type="xs:string" minOccurs="1" maxOccurs="1"/>
      <xs:element name="dataValue" type="xs:string" minOccurs="1" maxOccurs="1"/>
      <xs:element name="dataKey" type="xs:string" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="paymentSimpleType">
    <xs:sequence>
      <xs:choice>
        <xs:element name="creditCard" type="anet:creditCardSimpleType"/>
        <xs:element name="bankAccount" type="anet:bankAccountType"/>
      </xs:choice>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="paymentType">
    <xs:sequence>
      <xs:choice>
        <xs:element name="creditCard" type="anet:creditCardType"/>
        <xs:element name="bankAccount" type="anet:bankAccountType"/>
        <xs:element name="trackData" type="anet:creditCardTrackType"/>
        <xs:element name="encryptedTrackData" type="anet:encryptedTrackDataType"/>
        <xs:element name="payPal" type="anet:payPalType"/>
        <xs:element name="opaqueData" type="anet:opaqueDataType"/>
        <xs:element name="emv" type="anet:paymentEmvType"/>
      </xs:choice>
      <xs:element name="dataSource" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="paymentMaskedType">
    <xs:sequence>
      <xs:choice>
        <xs:element name="creditCard" type="anet:creditCardMaskedType"/>
        <xs:element name="bankAccount" type="anet:bankAccountMaskedType"/>
        <xs:element name="tokenInformation" type="anet:tokenMaskedType"/>
      </xs:choice>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="tokenMaskedType">
    <xs:sequence>
      <xs:element name="tokenSource" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="tokenNumber" type="xs:string" minOccurs="1" maxOccurs="1"/>
      <xs:element name="expirationDate" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="4"/>
            <xs:maxLength value="7"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
     <xs:element name="tokenRequestorId" type="xs:string" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="orderType">
    <xs:sequence>
      <xs:element name="invoiceNumber" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    
      <xs:element name="description" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="discountAmount" minOccurs="0" type="xs:decimal"/>        

      <xs:element name="taxIsAfterDiscount" type="xs:boolean" minOccurs="0">
      </xs:element>
      
      <xs:element name="totalTaxTypeCode" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="3"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="purchaserVATRegistrationNumber" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="21"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="merchantVATRegistrationNumber" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="21"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="vatInvoiceReferenceNumber" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="15"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="purchaserCode" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="17"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="summaryCommodityCode" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="purchaseOrderDateUTC" minOccurs="0" type="xs:date"/>
      

      <xs:element name="supplierOrderReference" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="25"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="authorizedContactName" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="36"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="cardAcceptorRefNumber" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="25"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="amexDataTAA1" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="amexDataTAA2" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="amexDataTAA3" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="amexDataTAA4" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

    </xs:sequence>
  </xs:complexType>
  
  <!-- ===================================================== -->
  <xs:complexType name="orderExType">
    <xs:complexContent>
      <xs:extension base="anet:orderType">
        <xs:sequence>
          <xs:element name="purchaseOrderNumber" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:maxLength value="25"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerType">
    <xs:sequence>
      <xs:element name="type" type="anet:customerTypeEnum" minOccurs="0"/>
      <xs:element name="id" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="email" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="phoneNumber" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="25"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="faxNumber" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="25"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="driversLicense" type="anet:driversLicenseType" minOccurs="0"/>
      <xs:element name="taxId" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="anet:numericString">
            <xs:minLength value="9"/>
            <xs:maxLength value="9"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerDataType">
    <xs:sequence>
      <xs:element name="type" type="anet:customerTypeEnum" minOccurs="0"/>
      <xs:element name="id" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="email" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="driversLicense" type="anet:driversLicenseType" minOccurs="0"/>
      <xs:element name="taxId" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="8"/>
            <xs:maxLength value="9"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="merchantAuthenticationType">
    <xs:sequence>
      <xs:element name="name" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="25"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <!-- name="MerchantAuthenticationItemChoiceType" -->
      <xs:choice minOccurs="1" maxOccurs="1">
        <xs:element name="transactionKey">
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:maxLength value="16"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element name="sessionToken" type="xs:string" minOccurs="0" maxOccurs="1"/>
        <xs:element name="password">
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:maxLength value="40"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element name="impersonationAuthentication" type="anet:impersonationAuthenticationType" minOccurs="0" maxOccurs="1"/>
        <xs:element name="fingerPrint" type="anet:fingerPrintType" minOccurs="0" maxOccurs="1"/>
        <xs:element name="clientKey" type="xs:string" minOccurs="0" maxOccurs="1"/>
        <xs:element maxOccurs="1" minOccurs="0" name="accessToken" type="xs:string"/>
      </xs:choice>
      <xs:element name="mobileDeviceId" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="60"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===========fingerPrint ========================== -->
  <xs:complexType name="fingerPrintType">
    <xs:sequence>
      <xs:element name="hashValue" type="xs:string" minOccurs="1" maxOccurs="1"/>
      <xs:element name="sequence" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="timestamp" type="xs:string" minOccurs="1" maxOccurs="1"/>
      <xs:element name="currencyCode" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="amount" type="xs:string" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===========Decryption Start ========================== -->
  <xs:complexType name="cardArt">
    <xs:sequence>
      <xs:element name="cardBrand" type="xs:string" minOccurs="0"/>
      <xs:element name="cardImageHeight" type="xs:string" minOccurs="0"/>
      <xs:element name="cardImageUrl" type="xs:string" minOccurs="0"/>
      <xs:element name="cardImageWidth" type="xs:string" minOccurs="0"/>
      <xs:element name="cardType" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="paymentDetails">
    <xs:sequence>
      <xs:element name="currency" type="xs:string" minOccurs="0"/>
      <xs:element name="promoCode" type="xs:string" minOccurs="0"/>
      <xs:element name="misc" type="xs:string" minOccurs="0"/>
      <xs:element name="giftWrap" type="xs:string" minOccurs="0"/>
      <xs:element name="discount" type="xs:string" minOccurs="0"/>
      <xs:element name="tax" type="xs:string" minOccurs="0"/>
      <xs:element name="shippingHandling" type="xs:string" minOccurs="0"/>
      <xs:element name="subTotal" type="xs:string" minOccurs="0"/>
      <xs:element name="orderID" type="xs:string" minOccurs="0"/>
      <xs:element name="amount" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="decryptPaymentDataRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="opaqueData" type="anet:opaqueDataType" minOccurs="1" maxOccurs="1"/>
            <xs:element name="callId" type="xs:string" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="decryptPaymentDataResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="shippingInfo" type="anet:customerAddressType" minOccurs="0"/>
            <xs:element name="billingInfo" type="anet:customerAddressType" minOccurs="0"/>
            <xs:element name="cardInfo" type="anet:creditCardMaskedType" minOccurs="0"/>
            <xs:element name="paymentDetails" type="anet:paymentDetails" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- ===========Decryption End ========================== -->
  <!-- ===========Encryption Start ========================== -->
  <xs:element name="securePaymentContainerRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="data" type="anet:webCheckOutDataType" minOccurs="1" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="securePaymentContainerResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="opaqueData" type="anet:opaqueDataType"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="webCheckOutDataType">
    <xs:sequence>
      <xs:element name="type" type="anet:webCheckOutTypeEnum" minOccurs="1" maxOccurs="1"/>
      <xs:element name="id" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="64"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
          <xs:element name="token" type="anet:webCheckOutDataTypeToken" minOccurs="0" maxOccurs="1"/>
          <xs:element name="bankToken" type="anet:bankAccountType" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
  </xs:complexType>
  <xs:complexType name="securePaymentContainerErrorType">
    <xs:sequence>
      <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1"/>
      <xs:element name="description" type="xs:string" minOccurs="1" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="webCheckOutTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="PAN"/>
      <xs:enumeration value="TOKEN"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===========Encryption End ========================== -->
  <!-- ===========META CREDENTIALS========================== -->
  <xs:complexType name="impersonationAuthenticationType">
    <xs:sequence>
      <xs:element name="partnerLoginId" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="25"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="partnerTransactionKey" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="16"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="paymentScheduleType">
    <xs:sequence>
      <!-- required for a new schedule, optional when updating -->
      <xs:element name="interval" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="length">
              <xs:simpleType>
                <xs:restriction base="xs:short">
                  <xs:minInclusive value="1"/>
                  <xs:maxInclusive value="32000"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="unit" type="anet:ARBSubscriptionUnitEnum"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!-- required for a new schedule, not allowed when editting existing subscription -->
      <xs:element name="startDate" type="xs:date" minOccurs="0"/>
      <!-- required for a new schedule, optional when updating -->
      <xs:element name="totalOccurrences" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:short">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="32000"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <!-- trialOccurrences is always optional -->
      <xs:element name="trialOccurrences" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:short">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="32000"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ARBSubscriptionType">
    <xs:sequence>
      <xs:element name="name" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="50"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <!-- paymentSchedule is required for a new subscription, optional if updating existing subscription -->
      <xs:element name="paymentSchedule" type="anet:paymentScheduleType" minOccurs="0"/>
      <xs:element name="amount" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0.01"/>
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="trialAmount" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="4"/>
            <xs:minInclusive value="0.00"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <!-- required for Create, optional on Update -->
      <xs:element name="payment" type="anet:paymentType" minOccurs="0"/>
      <xs:element name="order" type="anet:orderType" minOccurs="0"/>
      <xs:element name="customer" type="anet:customerType" minOccurs="0"/>
      <xs:element name="billTo" type="anet:nameAndAddressType" minOccurs="0"/>
      <xs:element name="shipTo" type="anet:nameAndAddressType" minOccurs="0"/>
      <xs:element name="profile" type="anet:customerProfileIdType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ARBSubscriptionMaskedType">
    <xs:sequence>
      <xs:element name="name" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="50"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <!-- paymentSchedule is required for a new subscription, optional if updating existing subscription -->
      <xs:element name="paymentSchedule" type="anet:paymentScheduleType" minOccurs="0"/>
      <xs:element name="amount" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0.01"/>
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="trialAmount" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="4"/>
            <xs:minInclusive value="0.00"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="status" type="anet:ARBSubscriptionStatusEnum" minOccurs="0"/>
      <xs:element name="profile" type="anet:subscriptionCustomerProfileType" minOccurs="0"/>
      <xs:element name="order" type="anet:orderType" minOccurs="0"/>
      <xs:element name="arbTransactions" type="anet:ARBTransactionList" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="subscriptionCustomerProfileType">
    <xs:complexContent>
      <xs:extension base="anet:customerProfileExType">
        <xs:sequence>
          <xs:element name="paymentProfile" type="anet:customerPaymentProfileMaskedType" minOccurs="0" maxOccurs="1"/>
          <xs:element name="shippingProfile" type="anet:customerAddressExType" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="subscriptionPaymentType">
    <xs:sequence>
      <xs:element name="id">
        <xs:simpleType>
          <xs:restriction base="xs:int">
            <xs:minInclusive value="0"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="payNum">
        <xs:simpleType>
          <xs:restriction base="xs:int">
            <xs:minInclusive value="0"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="fraudInformationType">
    <xs:sequence>
      <xs:element name="fraudFilterList" type="anet:ArrayOfFraudFilterType" minOccurs="1" maxOccurs="1"/>
      <xs:element name="fraudAction">
        <xs:simpleType>
          <xs:restriction base="xs:string"/>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfFraudFilterType">
    <xs:sequence>
      <xs:element name="fraudFilter" type="xs:string" minOccurs="1" maxOccurs="1000"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="mobileDeviceType">
    <xs:sequence>
      <xs:element name="mobileDeviceId" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="60"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="description" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="60"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="phoneNumber" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="devicePlatform" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="deviceActivation" type="anet:deviceActivationEnum" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
    <!-- 
	===================================================================
	subMerchantType
	These are the fields that are used for a transaction request.
	===================================================================
	-->
    <xs:complexType name="subMerchantType">
        <xs:sequence>
            <xs:element name="identifier" minOccurs="1" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="40" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="doingBusinessAs" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="50" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="paymentServiceProviderName" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="40" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="paymentServiceFacilitator" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="20" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="streetAddress" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="40" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="phone" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="20" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="email" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="40" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="postalCode" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="20" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="city" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="30" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="regionCode" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="10" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="countryCode" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="10" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- 
	===================================================================
	transactionRequestType
	These are the fields that are used for a transaction request.
	===================================================================
	-->
	<xs:complexType name="transactionRequestType">
		<xs:sequence>
			<xs:element name="transactionType" type="xs:string" />
			<xs:element name="amount" type="xs:decimal" minOccurs="0" />
      <xs:element name="currencyCode" type="xs:string" minOccurs="0" maxOccurs="1"></xs:element>
			<xs:element name="payment" type="anet:paymentType" minOccurs="0" />
      <xs:element name="profile" type="anet:customerProfilePaymentType" minOccurs="0"/>
      <xs:element name="solution" type="anet:solutionType" minOccurs="0"/>
      <xs:element name="callId" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="terminalNumber" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="authCode" type="xs:string" minOccurs="0"/>
      <xs:element name="refTransId" type="xs:string" minOccurs="0"/>
      <xs:element name="splitTenderId" type="xs:string" minOccurs="0"/>
      <xs:element name="order" type="anet:orderType" minOccurs="0"/>
      <xs:element name="lineItems" type="anet:ArrayOfLineItem" minOccurs="0"/>
      <xs:element name="tax" type="anet:extendedAmountType" minOccurs="0"/>      
      <xs:element name="duty" type="anet:extendedAmountType" minOccurs="0"/>
      <xs:element name="shipping" type="anet:extendedAmountType" minOccurs="0"/>
      <xs:element name="taxExempt" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:boolean"/>
        </xs:simpleType>
      </xs:element>
      <xs:element name="poNumber" type="xs:string" minOccurs="0"/>
      <xs:element name="customer" type="anet:customerDataType" minOccurs="0"/>
      <xs:element name="billTo" type="anet:customerAddressType" minOccurs="0"/>
      <xs:element name="shipTo" type="anet:nameAndAddressType" minOccurs="0"/>      
      <xs:element name="customerIP" type="xs:string" minOccurs="0"/>
      <xs:element name="cardholderAuthentication" type="anet:ccAuthenticationType" minOccurs="0"/>
      <xs:element name="retail" type="anet:transRetailInfoType" minOccurs="0"/>
      <xs:element name="employeeId" type="xs:string" minOccurs="0"/>
      <xs:element name="transactionSettings" type="anet:ArrayOfSetting" minOccurs="0">
        <xs:annotation>
          <xs:documentation>Allowed values for settingName are: emailCustomer, merchantEmail, allowPartialAuth, headerEmailReceipt, footerEmailReceipt, recurringBilling, duplicateWindow, testRequest.</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="userFields" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="userField" type="anet:userField" minOccurs="0" maxOccurs="20"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="surcharge" type="anet:extendedAmountType" minOccurs="0"/>
      <xs:element name="merchantDescriptor" type="xs:string" minOccurs="0" />
      <xs:element name="subMerchant" type="anet:subMerchantType" minOccurs="0" maxOccurs="1" />
      <xs:element name="tip" type="anet:extendedAmountType" minOccurs="0"/>
      <xs:element name="processingOptions" type="anet:processingOptions" minOccurs="0" maxOccurs="1"/>
      <xs:element name="subsequentAuthInformation" type="anet:subsequentAuthInformation" minOccurs="0" maxOccurs="1"/>
      <xs:element name="otherTax" minOccurs="0" type="anet:otherTaxType" />
      <xs:element name="shipFrom" minOccurs="0" type="anet:nameAndAddressType" />
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="KeyManagementScheme">
    <xs:sequence>
      <xs:element name="DUKPT">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Operation" type="anet:OperationType"/>
            <xs:element name="Mode">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="PIN" type="xs:string" minOccurs="0"/>
                  <xs:element name="Data" type="xs:string" minOccurs="0"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="DeviceInfo">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Description" type="xs:string"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="EncryptedData">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Value" type="xs:string"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="OperationType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="DECRYPT"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="EncryptionAlgorithmType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="TDES"/>
      <xs:enumeration value="AES"/>
      <xs:enumeration value="RSA"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="EncodingType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Base64"/>
      <xs:enumeration value="Hex"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="KeyValue">
    <xs:sequence>
      <xs:element name="Encoding" type="anet:EncodingType"/>
      <xs:element name="EncryptionAlgorithm" type="anet:EncryptionAlgorithmType"/>
      <xs:element name="Scheme" type="anet:KeyManagementScheme"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="KeyBlock">
    <xs:sequence>
      <xs:element name="Value" type="anet:KeyValue"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="encryptedTrackDataType">
    <xs:sequence>
      <xs:element name="FormOfPayment" type="anet:KeyBlock"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="settingType">
    <xs:sequence>
      <xs:element name="settingName" type="xs:string" minOccurs="0"/>
      <xs:element name="settingValue" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="permissionType">
    <xs:sequence>
      <xs:element name="permissionName" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:simpleType name="settingNameEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="emailCustomer">
        <xs:annotation>
          <xs:documentation>true/false. Used by createTransaction method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="merchantEmail">
        <xs:annotation>
          <xs:documentation>string. Used by createTransaction method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="allowPartialAuth">
        <xs:annotation>
          <xs:documentation>true/false. Used by createTransaction method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="headerEmailReceipt">
        <xs:annotation>
          <xs:documentation>string. Used by createTransaction method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="footerEmailReceipt">
        <xs:annotation>
          <xs:documentation>string. Used by createTransaction method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="recurringBilling">
        <xs:annotation>
          <xs:documentation>true/false. Used by createTransaction method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="duplicateWindow">
        <xs:annotation>
          <xs:documentation>number. Used by createTransaction method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="testRequest">
        <xs:annotation>
          <xs:documentation>true/false. Used by createTransaction method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfileReturnUrl">
        <xs:annotation>
          <xs:documentation>string. Used by getHostedProfilePage method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfileReturnUrlText">
        <xs:annotation>
          <xs:documentation>string. Used by getHostedProfilePage method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfilePageBorderVisible">
        <xs:annotation>
          <xs:documentation>true/false. Used by getHostedProfilePage method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfileIFrameCommunicatorUrl">
        <xs:annotation>
          <xs:documentation>string. Used by getHostedProfilePage method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfileHeadingBgColor">
        <xs:annotation>
          <xs:documentation>#e0e0e0. Used by getHostedProfilePage method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfileValidationMode">
        <xs:annotation>
          <xs:documentation>
      liveMode/testMode
      liveMode: generates a transaction to the processor in the amount of 0.01 or 0.00. is immediately voided, if successful.
      testMode: performs field validation only, all fields are validated except unrestricted field definitions (viz. telephone number) do not generate errors.
      If a validation transaction is unsuccessful, the profile is not created, and the merchant receives an error.
      </xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfileBillingAddressRequired">
        <xs:annotation>
          <xs:documentation>true/false. If true, sets First Name, Last Name, Address, City, State, and Zip Code as required fields in order for a payment profile to be created or updated within a hosted CIM form.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfileCardCodeRequired">
        <xs:annotation>
          <xs:documentation>true/false. If true, sets the Card Code field as required in order for a payment profile to be created or updated within a hosted CIM form.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfileBillingAddressOptions">
        <xs:annotation>
          <xs:documentation>
      showBillingAddress/showNone
      showBillingAddress: Allow merchant to show billing address.
      showNone : Hide billing address and billing name.
      </xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfileManageOptions">
        <xs:annotation>
          <xs:documentation>
      showAll/showPayment/ShowShipping
      showAll: Shipping and Payment profiles are shown on the manage page, this is the default.
      showPayment : Only Payment profiles are shown on the manage page.
      showShipping : Only Shippiung profiles are shown on the manage page.
      </xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedPaymentIFrameCommunicatorUrl">
        <xs:annotation>
          <xs:documentation>JSON string. Used by getHostedPaymentPage method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedPaymentButtonOptions">
        <xs:annotation>
          <xs:documentation>JSON string. Used by getHostedPaymentPage method.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedPaymentReturnOptions">
        <xs:annotation>
          <xs:documentation>JSON string. Used by getHostedPaymentPage method</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedPaymentOrderOptions">
        <xs:annotation>
          <xs:documentation>JSON string. Used by getHostedPaymentPage method</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedPaymentPaymentOptions">
        <xs:annotation>
          <xs:documentation>JSON string. Used by getHostedPaymentPage method</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedPaymentBillingAddressOptions">
        <xs:annotation>
          <xs:documentation>JSON string. Used by getHostedPaymentPage method</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedPaymentShippingAddressOptions">
        <xs:annotation>
          <xs:documentation>JSON string. Used by getHostedPaymentPage method</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedPaymentSecurityOptions">
        <xs:annotation>
          <xs:documentation>JSON string. Used by getHostedPaymentPage method</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedPaymentCustomerOptions">
        <xs:annotation>
          <xs:documentation>JSON string. Used by getHostedPaymentPage method</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedPaymentStyleOptions">
        <xs:annotation>
          <xs:documentation>JSON string. Used by getHostedPaymentPage method</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="typeEmailReceipt">
        <xs:annotation>
          <xs:documentation>JSON string. Used by sendCustomerTransactionReceipt method</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
       <xs:enumeration value="hostedProfilePaymentOptions">
        <xs:annotation>
          <xs:documentation>
            showAll/showCreditCard/showBankAccount
            showAll: both CreditCard and BankAccount sections will be shown on Add payment page, this is the default.
            showCreditCard :only CreditCard payment form will be shown on Add payment page.
            showBankAccount :only BankAccount payment form will be shown on Add payment page.
          </xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="hostedProfileSaveButtonText">
        <xs:annotation>
          <xs:documentation>string. Used by getHostedProfilePage method to accept button text configuration.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <!-- ===================================================== -->
  <xs:complexType name="userField">
    <xs:sequence>
      <xs:element name="name" type="xs:string" minOccurs="0"/>
      <xs:element name="value" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="emvTag">
    <xs:sequence>
      <xs:element name="name" type="xs:string" minOccurs="0"/>
      <xs:element name="value" type="xs:string" minOccurs="0"/>
      <xs:element name="formatted" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerPaymentProfileBaseType">
    <xs:sequence>
      <xs:element name="customerType" type="anet:customerTypeEnum" minOccurs="0"/>
      <xs:element name="billTo" type="anet:customerAddressType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerPaymentProfileType">
    <xs:complexContent>
      <xs:extension base="anet:customerPaymentProfileBaseType">
        <xs:sequence>
          <xs:element name="payment" type="anet:paymentType" minOccurs="0"/>
          <xs:element name="driversLicense" type="anet:driversLicenseType" minOccurs="0"/>
          <!-- Format of taxId should be numeric string or four X's followed by the last four digits. -->
          <xs:element name="taxId" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:minLength value="8"/>
                <xs:maxLength value="9"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
          <xs:element name="defaultPaymentProfile" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerPaymentProfileExType">
    <xs:complexContent>
      <xs:extension base="anet:customerPaymentProfileType">
        <xs:sequence>
          <xs:element name="customerPaymentProfileId" type="anet:numericString" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerPaymentProfileMaskedType">
    <xs:complexContent>
      <xs:extension base="anet:customerPaymentProfileBaseType">
        <xs:sequence>
          <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0"/>
          <xs:element name="customerPaymentProfileId" type="anet:numericString" minOccurs="1"/>
          <xs:element name="defaultPaymentProfile" type="xs:boolean" minOccurs="0"/>
          <xs:element name="payment" type="anet:paymentMaskedType" minOccurs="0"/>
          <xs:element name="driversLicense" type="anet:driversLicenseMaskedType" minOccurs="0"/>
          <xs:element name="taxId" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:length value="8"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
          <xs:element name="subscriptionIds" type="anet:SubscriptionIdList" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="SubscriptionIdList">
    <xs:sequence>
      <xs:element name="subscriptionId" type="anet:numericString" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerProfileBaseType">
    <xs:sequence>
      <xs:element name="merchantCustomerId" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="description" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="email" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerProfileType">
    <xs:complexContent>
      <xs:extension base="anet:customerProfileBaseType">
        <xs:sequence>
          <xs:element name="paymentProfiles" type="anet:customerPaymentProfileType" minOccurs="0" maxOccurs="unbounded" />
          <xs:element name="shipToList" type="anet:customerAddressType" minOccurs="0" maxOccurs="unbounded" />
          <xs:element name="profileType"	type="anet:customerProfileTypeEnum" minOccurs="0" maxOccurs="1" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="paymentEmvType">
    <xs:sequence>
      <xs:element name="emvData" minOccurs="1" maxOccurs="1"/>
      <xs:element name="emvDescriptor" minOccurs="1" maxOccurs="1"/>
      <xs:element name="emvVersion" minOccurs="1" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerProfileExType">
    <xs:complexContent>
      <xs:extension base="anet:customerProfileBaseType">
        <xs:sequence>
          <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerProfileInfoExType">
    <xs:complexContent>
      <xs:extension base="anet:customerProfileExType">
        <xs:sequence>
          <xs:element name="profileType"	type="anet:customerProfileTypeEnum" minOccurs="0" maxOccurs="1" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerProfileMaskedType">
    <xs:complexContent>
      <xs:extension base="anet:customerProfileExType">
        <xs:sequence>
          <xs:element name="paymentProfiles" type="anet:customerPaymentProfileMaskedType" minOccurs="0" maxOccurs="unbounded" />
          <xs:element name="shipToList" type="anet:customerAddressExType" minOccurs="0" maxOccurs="unbounded" />
          <xs:element name="profileType" type="anet:customerProfileTypeEnum" minOccurs="0" maxOccurs="1" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerAddressType">
    <xs:complexContent>
      <xs:extension base="anet:nameAndAddressType">
        <xs:sequence>
          <xs:element name="phoneNumber" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:maxLength value="25"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
          <xs:element name="faxNumber" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:maxLength value="25"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
          <xs:element name="email" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="customerAddressExType">
    <xs:complexContent>
      <xs:extension base="anet:customerAddressType">
        <xs:sequence>
          <xs:element name="customerAddressId" type="anet:numericString" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="extendedAmountType">
    <xs:sequence>
      <xs:element name="amount">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0.00"/>
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="name" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="31"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="description" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <!-- ===================================================== -->
  <xs:complexType name="otherTaxType">
    <xs:sequence>

      <xs:element name="nationalTaxAmount" minOccurs="0" type="xs:decimal" />        
      <xs:element name="localTaxAmount" minOccurs="0" type="xs:decimal" />
      <xs:element name="alternateTaxAmount" minOccurs="0" type="xs:decimal" />        
      <xs:element name="alternateTaxId" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="15"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="vatTaxRate" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="5"/>
            <xs:fractionDigits value="5"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="vatTaxAmount" minOccurs="0"  type="xs:decimal" />      
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <!-- ===================================================== -->  
  <xs:complexType name="lineItemType">
    <xs:sequence>
      <xs:element name="itemId">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="31"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="name">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="31"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="description" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="quantity">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0.00"/>
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="unitPrice">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0.00"/>
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="taxable" minOccurs="0" type="xs:boolean" />
      
      <xs:element name="unitOfMeasure" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="12"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="typeOfSupply" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="2"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="taxRate" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="5"/>
            <xs:fractionDigits value="5"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="taxAmount" minOccurs="0" type="xs:decimal" />
      <xs:element name="nationalTax" minOccurs="0" type="xs:decimal"/>
      <xs:element name="localTax" minOccurs="0" type="xs:decimal"/>
      <xs:element name="vatRate" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="5"/>
            <xs:fractionDigits value="5"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="alternateTaxId" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="alternateTaxType" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="alternateTaxTypeApplied" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="alternateTaxRate" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="5" />
            <xs:fractionDigits value="5" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="alternateTaxAmount" minOccurs="0" type="xs:decimal" />        
      <xs:element name="totalAmount" minOccurs="0" type="xs:decimal" />     
      <xs:element name="commodityCode" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="15"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="productCode" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="15"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="productSKU" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="30"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="discountRate" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="5" />
            <xs:fractionDigits value="5" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>

      <xs:element name="discountAmount" minOccurs="0" type="xs:decimal" />       
      <xs:element name="taxIncludedInTotal" minOccurs="0" type ="xs:boolean" />        
      <xs:element name="taxIsAfterDiscount" minOccurs="0" type="xs:boolean" >
      </xs:element>
      
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="profileTransAmountType">
    <xs:sequence>
      <xs:element name="amount">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0.01"/>
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="tax" type="anet:extendedAmountType" minOccurs="0"/>
      <xs:element name="shipping" type="anet:extendedAmountType" minOccurs="0"/>
      <xs:element name="duty" type="anet:extendedAmountType" minOccurs="0"/>
      <xs:element name="lineItems" type="anet:lineItemType" minOccurs="0" maxOccurs="30"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="profileTransOrderType">
    <xs:complexContent>
      <xs:extension base="anet:profileTransAmountType">
        <xs:sequence>
          <xs:element name="customerProfileId" type="anet:numericString"/>
          <xs:element name="customerPaymentProfileId" type="anet:numericString"/>
          <xs:element name="customerShippingAddressId" type="anet:numericString" minOccurs="0"/>
          <xs:element name="order" type="anet:orderExType" minOccurs="0"/>
          <xs:element name="taxExempt" type="xs:boolean" minOccurs="0"/>
          <xs:element name="recurringBilling" type="xs:boolean" minOccurs="0"/>
          <xs:element name="cardCode" type="anet:cardCode" minOccurs="0" maxOccurs="1"/>
          <xs:element name="splitTenderId" type="anet:numericString" minOccurs="0"/>
          <xs:element name="processingOptions" type="anet:processingOptions" minOccurs="0" maxOccurs="1"/>
          <xs:element name="subsequentAuthInformation" type="anet:subsequentAuthInformation" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="profileTransAuthCaptureType">
    <xs:complexContent>
      <xs:extension base="anet:profileTransOrderType"/>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="profileTransAuthOnlyType">
    <xs:complexContent>
      <xs:extension base="anet:profileTransOrderType"/>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="profileTransPriorAuthCaptureType">
    <xs:complexContent>
      <xs:extension base="anet:profileTransAmountType">
        <xs:sequence>
          <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0"/>
          <xs:element name="customerPaymentProfileId" type="anet:numericString" minOccurs="0"/>
          <xs:element name="customerShippingAddressId" type="anet:numericString" minOccurs="0"/>
          <xs:element name="transId" type="anet:numericString"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="profileTransCaptureOnlyType">
    <xs:complexContent>
      <xs:extension base="anet:profileTransOrderType">
        <xs:sequence>
          <xs:element name="approvalCode">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:maxLength value="6"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="profileTransRefundType">
    <xs:complexContent>
      <xs:extension base="anet:profileTransAmountType">
        <xs:sequence>
          <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0"/>
          <xs:element name="customerPaymentProfileId" type="anet:numericString" minOccurs="0"/>
          <xs:element name="customerShippingAddressId" type="anet:numericString" minOccurs="0"/>
          <!-- Format of creditCardNumberMasked should be four X's followed by the last four digits. -->
          <xs:element name="creditCardNumberMasked" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:minLength value="8"/>
                <xs:maxLength value="8"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
          <!-- Format of bankRoutingNumberMasked should be four X's followed by the last four digits. -->
          <xs:element name="bankRoutingNumberMasked" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:minLength value="8"/>
                <xs:maxLength value="8"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
          <!-- Format of bankAccountNumberMasked should be four X's followed by the last four digits. -->
          <xs:element name="bankAccountNumberMasked" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:minLength value="8"/>
                <xs:maxLength value="8"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
          <xs:element name="order" type="anet:orderExType" minOccurs="0"/>
          <xs:element name="transId" type="anet:numericString" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="profileTransVoidType">
    <xs:sequence>
      <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0"/>
      <xs:element name="customerPaymentProfileId" type="anet:numericString" minOccurs="0"/>
      <xs:element name="customerShippingAddressId" type="anet:numericString" minOccurs="0"/>
      <xs:element name="transId" type="anet:numericString"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="profileTransactionType">
    <xs:choice>
      <xs:element name="profileTransAuthCapture" type="anet:profileTransAuthCaptureType"/>
      <xs:element name="profileTransAuthOnly" type="anet:profileTransAuthOnlyType"/>
      <xs:element name="profileTransPriorAuthCapture" type="anet:profileTransPriorAuthCaptureType"/>
      <xs:element name="profileTransCaptureOnly" type="anet:profileTransCaptureOnlyType"/>
      <xs:element name="profileTransRefund" type="anet:profileTransRefundType"/>
      <xs:element name="profileTransVoid" type="anet:profileTransVoidType"/>
    </xs:choice>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="transactionSummaryType">
    <xs:sequence>
      <xs:element name="transId" type="anet:numericString"/>
      <xs:element name="submitTimeUTC" type="xs:dateTime"/>
      <xs:element name="submitTimeLocal" type="xs:dateTime"/>
      <xs:element name="transactionStatus" type="xs:string"/>
      <!-- anet:transactionStatusEnum -->
      <xs:element name="invoiceNumber" type="xs:string" minOccurs="0"/>
      <xs:element name="firstName" type="xs:string" minOccurs="0"/>
      <xs:element name="lastName" type="xs:string" minOccurs="0"/>
      <xs:element name="accountType" type="xs:string"/>
      <!-- anet:accountTypeEnum -->
      <xs:element name="accountNumber" type="xs:string"/>
      <xs:element name="settleAmount" type="xs:decimal"/>
      <xs:element name="marketType" type="xs:string" minOccurs="0"/>
      <!-- anet:marketTypeEnum -->
      <xs:element name="product" type="xs:string" minOccurs="0"/>
      <!-- anet:productEnum -->
      <xs:element name="mobileDeviceId" type="xs:string" minOccurs="0"/>
      <xs:element name="subscription" type="anet:subscriptionPaymentType" minOccurs="0"/>
      <xs:element name="hasReturnedItems" type="xs:boolean" minOccurs="0"/>
      <xs:element name="fraudInformation" type="anet:fraudInformationType" minOccurs="0"/>
      <xs:element name="profile" type="anet:customerProfileIdType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="arbTransaction">
    <xs:sequence>
      <xs:element name="transId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
      <xs:element name="response" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="submitTimeUTC" type="xs:dateTime" minOccurs="0" maxOccurs="1"/>
      <xs:element name="payNum" type="xs:int" minOccurs="0" maxOccurs="1"/>
      <xs:element name="attemptNum" type="xs:int" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="transactionDetailsType">
    <xs:sequence>
      <xs:element name="transId" type="anet:numericString"/>
      <xs:element name="refTransId" type="anet:numericString" minOccurs="0"/>
      <xs:element name="splitTenderId" type="anet:numericString" minOccurs="0"/>
      <xs:element name="submitTimeUTC" type="xs:dateTime"/>
      <xs:element name="submitTimeLocal" type="xs:dateTime"/>
      <xs:element name="transactionType" type="xs:string"/>
      <!-- anet:transactionTypeEnum -->
      <xs:element name="transactionStatus" type="xs:string"/>
      <!-- anet:transactionStatusEnum -->
      <xs:element name="responseCode" type="xs:int"/>
      <xs:element name="responseReasonCode" type="xs:int"/>
      <xs:element name="subscription" type="anet:subscriptionPaymentType" minOccurs="0"/>
      <xs:element name="responseReasonDescription" type="xs:string"/>
      <xs:element name="authCode" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="6"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="AVSResponse" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="1"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="cardCodeResponse" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="1"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="CAVVResponse" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="1"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="FDSFilterAction" type="xs:string" minOccurs="0"/>
      <!-- anet:FDSFilterActionEnum -->
      <xs:element name="FDSFilters" type="anet:ArrayOfFDSFilter" minOccurs="0"/>
      <xs:element name="batch" type="anet:batchDetailsType" minOccurs="0"/>
      <xs:element name="order" type="anet:orderExType" minOccurs="0"/>
      <xs:element name="requestedAmount" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0.00"/>
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="authAmount">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0.00"/>
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="settleAmount">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0.00"/>
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="tax" type="anet:extendedAmountType" minOccurs="0"/>      
      <xs:element name="shipping" type="anet:extendedAmountType" minOccurs="0"/>
      <xs:element name="duty" type="anet:extendedAmountType" minOccurs="0"/>
      <xs:element name="lineItems" type="anet:ArrayOfLineItem" minOccurs="0"/>
      <xs:element name="prepaidBalanceRemaining" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="taxExempt" type="xs:boolean" minOccurs="0"/>
      <xs:element name="payment" type="anet:paymentMaskedType"/>
      <xs:element name="customer" type="anet:customerDataType" minOccurs="0"/>
      <xs:element name="billTo" type="anet:customerAddressType" minOccurs="0"/>
      <xs:element name="shipTo" type="anet:nameAndAddressType" minOccurs="0"/>      
      <xs:element name="recurringBilling" type="xs:boolean" minOccurs="0"/>
      <xs:element name="customerIP" type="xs:string" minOccurs="0"/>
      <xs:element name="product" type="xs:string" minOccurs="0"/>
      <xs:element name="entryMode" type="xs:string" minOccurs="0"/>
      <xs:element name="marketType" type="xs:string" minOccurs="0"/>
      <xs:element name="mobileDeviceId" type="xs:string" minOccurs="0"/>
      <xs:element name="customerSignature" type="xs:string" minOccurs="0"/>
      <xs:element name="returnedItems" type="anet:ArrayOfReturnedItem" minOccurs="0"/>
      <xs:element name="solution" type="anet:solutionType" minOccurs="0"/>
      <xs:element name="emvDetails" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="tag" minOccurs="1" maxOccurs="unbounded">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="tagId" type="xs:string"/>
                  <xs:element name="data" type="xs:string"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="profile" type="anet:customerProfileIdType" minOccurs="0"/>
      <xs:element name="surcharge" type="anet:extendedAmountType" minOccurs="0"/>
      <xs:element name="employeeId" type="xs:string" minOccurs="0" />
      <xs:element name="tip" type="anet:extendedAmountType" minOccurs="0"/>            
      <xs:element name="otherTax" minOccurs="0" type="anet:otherTaxType" />
      <xs:element name="shipFrom"  minOccurs="0" type="anet:nameAndAddressType" />
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="solutionType">
    <xs:sequence>
      <xs:element name="id" type="xs:string" maxOccurs="1"/>
      <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="vendorName" type="xs:string" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfReturnedItem">
    <xs:sequence>
      <xs:element name="returnedItem" type="anet:returnedItemType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="returnedItemType">
    <xs:sequence>
      <xs:element name="id" type="anet:numericString"/>
      <xs:element name="dateUTC" type="xs:dateTime"/>
      <xs:element name="dateLocal" type="xs:dateTime"/>
      <xs:element name="code" type="xs:string"/>
      <xs:element name="description" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="FDSFilterType">
    <xs:sequence>
      <xs:element name="name" type="xs:string"/>
      <xs:element name="action" type="xs:string"/>
      <!-- anet:FDSFilterActionEnum -->
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="batchDetailsType">
    <xs:sequence>
      <xs:element name="batchId" type="anet:numericString"/>
      <xs:element name="settlementTimeUTC" type="xs:dateTime" minOccurs="0"/>
      <xs:element name="settlementTimeLocal" type="xs:dateTime" minOccurs="0"/>
      <xs:element name="settlementState" type="xs:string"/>
      <!-- anet:settlementStateEnum -->
      <xs:element name="paymentMethod" type="xs:string" minOccurs="0"/>
      <!-- anet:paymentMethodEnum -->
      <xs:element name="marketType" type="xs:string" minOccurs="0"/>
      <!-- anet:marketTypeEnum -->
      <xs:element name="product" type="xs:string" minOccurs="0"/>
      <!-- anet:productEnum -->
      <xs:element name="statistics" type="anet:ArrayOfBatchStatisticType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="batchStatisticType">
    <xs:sequence>
      <xs:element name="accountType" type="xs:string"/>
      <!-- anet:accountTypeEnum -->
      <xs:element name="chargeAmount" type="xs:decimal"/>
      <xs:element name="chargeCount" type="xs:int"/>
      <xs:element name="refundAmount" type="xs:decimal"/>
      <xs:element name="refundCount" type="xs:int"/>
      <xs:element name="voidCount" type="xs:int"/>
      <xs:element name="declineCount" type="xs:int"/>
      <xs:element name="errorCount" type="xs:int"/>
      <xs:element name="returnedItemAmount" type="xs:decimal" minOccurs="0" maxOccurs="1"/>
      <xs:element name="returnedItemCount" type="xs:int" minOccurs="0"/>
      <xs:element name="chargebackAmount" type="xs:decimal" minOccurs="0"/>
      <xs:element name="chargebackCount" type="xs:int" minOccurs="0"/>
      <xs:element name="correctionNoticeCount" type="xs:int" minOccurs="0"/>
      <xs:element name="chargeChargeBackAmount" type="xs:decimal" minOccurs="0"/>
      <xs:element name="chargeChargeBackCount" type="xs:int" minOccurs="0"/>
      <xs:element name="refundChargeBackAmount" type="xs:decimal" minOccurs="0"/>
      <xs:element name="refundChargeBackCount" type="xs:int" minOccurs="0"/>
      <xs:element name="chargeReturnedItemsAmount" type="xs:decimal" minOccurs="0"/>
      <xs:element name="chargeReturnedItemsCount" type="xs:int" minOccurs="0"/>
      <xs:element name="refundReturnedItemsAmount" type="xs:decimal" minOccurs="0"/>
      <xs:element name="refundReturnedItemsCount" type="xs:int" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="transactionResponse">
    <xs:sequence>
      <xs:element name="responseCode" type="xs:string" minOccurs="0"/>
      <xs:element name="rawResponseCode" type="xs:string" minOccurs="0"/>
      <xs:element name="authCode" type="xs:string" minOccurs="0"/>
      <xs:element name="avsResultCode" type="xs:string" minOccurs="0"/>
      <xs:element name="cvvResultCode" type="xs:string" minOccurs="0"/>
      <xs:element name="cavvResultCode" type="xs:string" minOccurs="0"/>
      <xs:element name="transId" type="xs:string" minOccurs="0"/>
      <xs:element name="refTransID" type="xs:string" minOccurs="0"/>
      <xs:element name="transHash" type="xs:string" minOccurs="0"/>
      <xs:element name="testRequest" type="xs:string" minOccurs="0"/>
      <xs:element name="accountNumber" type="xs:string" minOccurs="0"/>
      <xs:element name="entryMode" type="xs:string" minOccurs="0"/>
      <xs:element name="accountType" type="xs:string" minOccurs="0"/>
      <xs:element name="splitTenderId" type="xs:string" minOccurs="0"/>
      <xs:element name="prePaidCard" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="requestedAmount" type="xs:string" minOccurs="0"/>
            <xs:element name="approvedAmount" type="xs:string" minOccurs="0"/>
            <xs:element name="balanceOnCard" type="xs:string" minOccurs="0"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="messages" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="message" minOccurs="0" maxOccurs="unbounded">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="code" type="xs:string" minOccurs="0"/>
                  <xs:element name="description" type="xs:string" minOccurs="0"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="errors" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="error" minOccurs="0" maxOccurs="unbounded">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="errorCode" type="xs:string" minOccurs="0"/>
                  <xs:element name="errorText" type="xs:string" minOccurs="0"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="splitTenderPayments" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="splitTenderPayment" minOccurs="0" maxOccurs="unbounded">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="transId" type="xs:string" minOccurs="0"/>
                  <xs:element name="responseCode" type="xs:string" minOccurs="0"/>
                  <xs:element name="responseToCustomer" type="xs:string" minOccurs="0"/>
                  <xs:element name="authCode" type="xs:string" minOccurs="0"/>
                  <xs:element name="accountNumber" type="xs:string" minOccurs="0"/>
                  <xs:element name="accountType" type="xs:string" minOccurs="0"/>
                  <xs:element name="requestedAmount" type="xs:string" minOccurs="0"/>
                  <xs:element name="approvedAmount" type="xs:string" minOccurs="0"/>
                  <xs:element name="balanceOnCard" type="xs:string" minOccurs="0"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="userFields" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="userField" type="anet:userField" minOccurs="0" maxOccurs="20"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="shipTo" type="anet:nameAndAddressType" minOccurs="0"/>
      <xs:element name="secureAcceptance" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="SecureAcceptanceUrl" type="xs:string" minOccurs="0"/>
            <xs:element name="PayerID" type="xs:string" minOccurs="0"/>
            <xs:element name="PayerEmail" type="xs:string" minOccurs="0"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="emvResponse" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="tlvData" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="tags" minOccurs="0">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="tag" type="anet:emvTag" minOccurs="1" maxOccurs="unbounded"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="transHashSha2" type="xs:string" minOccurs="0"/>
      <xs:element name="profile" type="anet:customerProfileIdType" minOccurs="0"/>
      <xs:element name="networkTransId" type="anet:networkTransId" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 
  =================================================================== 
  The ANetApiRequest defines elements common to all API method
  requests.
  =================================================================== 
  -->
  <xs:complexType name="ANetApiRequest">
    <xs:sequence>
      <xs:element name="merchantAuthentication" type="anet:merchantAuthenticationType"/>
      <!-- Identifier of the API client application/SDK/library/binding e.g. PHP-SDK-1.0.9.1, ACTIVE_MERCHANT, etc. -->
      <xs:element name="clientId" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="30"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="refId" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="emailSettingsType">
    <xs:annotation>
      <xs:documentation>Allowed values for settingName are: headerEmailReceipt and footerEmailReceipt</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="anet:ArrayOfSetting">
        <xs:attribute name="version" use="optional">
          <xs:simpleType>
            <xs:restriction base="xs:integer"/>
          </xs:simpleType>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- 
  xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   Response type definitions begin here
  xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  -->
  <!-- 
  ===================================================================
  The messagesType provides the result of the request. The resultCode
  element provides the overall result of the request. The individual
  message(s) provide more detail, especially for errors, about the result.
  
  Ok - The request was processed and accepted without error. If any
    messages are present they will be informational only.
  Error - The request resulted in one or more errors. See messages
    for details.
  ===================================================================
  -->
  <xs:simpleType name="messageTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Ok"/>
      <xs:enumeration value="Error"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="messagesType">
    <xs:sequence>
      <xs:element name="resultCode" type="anet:messageTypeEnum"/>
      <xs:element name="message" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="code" type="xs:string"/>
            <xs:element name="text" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!-- 
  ===================================================================
  The ANetApiResponse defines elements common to all API method 
  responses.
  ===================================================================
  -->
  <xs:complexType name="ANetApiResponse">
    <xs:sequence>
      <xs:element name="refId" type="xs:string" minOccurs="0"/>
      <xs:sequence>
        <xs:element name="messages" type="anet:messagesType"/>
      </xs:sequence>
      <xs:element name="sessionToken" type="xs:string" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 
  xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   API method definitions begin here       
  xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  -->
  <!-- 
  ===================================================================
  errorResponse
  This is the response when an error occurs before the method can
  be determined, such as an "unknown method" type of error.
  ===================================================================
  -->
  <xs:element name="ErrorResponse" type="anet:ANetApiResponse"/>
  <!-- 
  ===================================================================
  isAliveRequest
  This method is used to test the availability of the API.
  ===================================================================
  -->
  <xs:element name="isAliveRequest">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="refId" minOccurs="0">
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:maxLength value="20"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  isAliveResponse
  This is the response to isAliveRequest.
  ===================================================================
  -->
  <xs:element name="isAliveResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  authenticateTestRequest
  This method is used to test the availability of the API.
  ===================================================================
  -->
  <xs:element name="authenticateTestRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  authenticateTestResponse
  This is the response to authenticateTestRequest.
  ===================================================================
  -->
  <xs:element name="authenticateTestResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  ARBCreateSubscriptionRequest
  This method is used to create a new ARB subscription.
  The merchant must be signed up for the ARB service to use it.
  ===================================================================
  -->
  <xs:element name="ARBCreateSubscriptionRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="subscription" type="anet:ARBSubscriptionType"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  ARBCreateSubscriptionResponse
  This is the response to ARBCreateSubscriptionRequest.
  ===================================================================
  -->
  <xs:element name="ARBCreateSubscriptionResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <!-- subscriptionId will only be present if a subscription was created. -->
            <xs:element name="subscriptionId" type="anet:numericString" minOccurs="0"/>
            <xs:element name="profile" type="anet:customerProfileIdType" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  ARBUpdateSubscriptionRequest
  This method is used to update an existing ARB subscription.
  The merchant must be signed up for the ARB service to use it.
  ===================================================================
  -->
  <xs:element name="ARBUpdateSubscriptionRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="subscriptionId" type="anet:numericString"/>
            <xs:element name="subscription" type="anet:ARBSubscriptionType"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  ARBUpdateSubscriptionResponse
  This is the response to ARBUpdateSubscriptionResponse.
  ===================================================================
  -->
  <xs:element name="ARBUpdateSubscriptionResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="profile" type="anet:customerProfileIdType" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  ARBCancelSubscriptionRequest
  This method is used to cancel an existing ARB subscription.
  The merchant must be signed up for the ARB service to use it.
  ===================================================================
  -->
  <xs:element name="ARBCancelSubscriptionRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="subscriptionId" type="anet:numericString"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  ARBCancelSubscriptionResponse
  This is the response to ARBCancelSubscriptionRequest.
  ===================================================================
  -->
  <xs:element name="ARBCancelSubscriptionResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  ARBGetSubscriptionStatusRequest
  This method is used to get the status of an existing ARB subscription.
  The merchant must be signed up for the ARB service to use it.
  ===================================================================
  -->
  <xs:element name="ARBGetSubscriptionStatusRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="subscriptionId" type="anet:numericString"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  ARBGetSubscriptionStatusResponse
  This is the response to ARBGetSubscriptionStatusRequest.
  ===================================================================
  -->
  <xs:element name="ARBGetSubscriptionStatusResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="status" type="anet:ARBSubscriptionStatusEnum" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  createCustomerProfileRequest
  This method is used to create a new customer profile along with any 
  customer payment profiles and customer shipping addresses for the customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="createCustomerProfileRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="profile" type="anet:customerProfileType"/>
            <xs:element name="validationMode" type="anet:validationModeEnum" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  createCustomerProfileResponse
  This is the response to createCustomerProfileRequest.
  ===================================================================
  -->
  <xs:element name="createCustomerProfileResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <!-- customerProfileId will only be present if a profile was created. -->
            <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0"/>
            <xs:element name="customerPaymentProfileIdList" type="anet:ArrayOfNumericString"/>
            <xs:element name="customerShippingAddressIdList" type="anet:ArrayOfNumericString"/>
            <xs:element name="validationDirectResponseList" type="anet:ArrayOfString"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  createProfileResponse
  This is the partial response for createProfile in the transactionRequestType.
  ===================================================================
  -->
  <xs:complexType name="createProfileResponse">
    <xs:sequence>
      <xs:element name="messages" type="anet:messagesType" minOccurs="1" maxOccurs="1"/>
      <!-- customerProfileId will only be present if a profile was created. -->
      <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
      <xs:element name="customerPaymentProfileIdList" type="anet:ArrayOfNumericString" minOccurs="0" maxOccurs="1"/>
      <xs:element name="customerShippingAddressIdList" type="anet:ArrayOfNumericString" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 
  ===================================================================
  createCustomerPaymentProfileRequest
  This method is used to create a new customer payment profile for an existing customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="createCustomerPaymentProfileRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="paymentProfile" type="anet:customerPaymentProfileType"/>
            <xs:element name="validationMode" type="anet:validationModeEnum" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  createCustomerPaymentProfileResponse
  This is the response to createCustomerPaymentProfileRequest.
  ===================================================================
  -->
  <xs:element name="createCustomerPaymentProfileResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <!-- customerPaymentProfileId and customerProfileId  will only be present 
      if a payment profile was created or a duplicate payment profile was found. -->
            <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0"/>
            <xs:element name="customerPaymentProfileId" type="anet:numericString" minOccurs="0"/>
            <!-- validationDirectResponse will only be present if validationMode is testMode or liveMode. -->
            <xs:element name="validationDirectResponse" minOccurs="0">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="2048"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  createCustomerShippingAddressRequest
  This method is used to create a new customer shipping address for an existing customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="createCustomerShippingAddressRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="address" type="anet:customerAddressType"/>
            <xs:element name="defaultShippingAddress" type="xs:boolean" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  createCustomerShippingAddressResponse
  This is the response to createCustomerShippingAddressRequest.
  ===================================================================
  -->
  <xs:element name="createCustomerShippingAddressResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <!-- customerAddressId and customerProfileId will only be present 
      if a shipping address was created or a duplicate shipping address was found. -->
            <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0"/>
            <xs:element name="customerAddressId" type="anet:numericString" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  createCustomerProfileFromTransactionRequest
  This method is used to create a Customer profile, payment profile, and shipping profile 
  from an existing successful transaction.
  ===================================================================
  -->
  <xs:element name="createCustomerProfileFromTransactionRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="transId" type="anet:numericString" minOccurs="1" maxOccurs="1"/>
            <xs:element name="customer" type="anet:customerProfileBaseType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
            <xs:element name="defaultPaymentProfile" type="xs:boolean" minOccurs="0"/>
            <xs:element name="defaultShippingAddress" type="xs:boolean" minOccurs="0"/>
            <xs:element name="profileType" type="anet:customerProfileTypeEnum" minOccurs="0" maxOccurs="1" />
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getCustomerProfileRequest
  This method is used to retrieve an existing customer profile along with all the 
  customer payment profiles and customer shipping addresses for the customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="getCustomerProfileRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
            <xs:element name="merchantCustomerId" minOccurs="0" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="20"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="email" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="unmaskExpirationDate" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
            <xs:element name="includeIssuerInfo" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getCustomerProfileResponse
  This is the response to getCustomerProfileRequest.
  ===================================================================
  -->
  <xs:element name="getCustomerProfileResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <!-- profile will only be present if a profile was successfully retrieved. -->
            <xs:element name="profile" type="anet:customerProfileMaskedType" minOccurs="0"/>
            <xs:element name="subscriptionIds" type="anet:SubscriptionIdList" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getCustomerPaymentProfileRequest
  This method is used to retrieve an existing customer payment profile for a customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="getCustomerPaymentProfileRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="customerPaymentProfileId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
            <xs:element name="unmaskExpirationDate" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
            <xs:element name="includeIssuerInfo" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getCustomerPaymentProfileResponse
  This is the response to getCustomerPaymentProfileRequest.
  ===================================================================
  -->
  <xs:element name="getCustomerPaymentProfileResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <!-- paymentProfile and customerProfileId will only be present if a payment profile was successfully retrieved. -->
            <xs:element name="paymentProfile" type="anet:customerPaymentProfileMaskedType" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getCustomerShippingAddressRequest
  This method is used to retrieve an existing customer shipping address for a customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="getCustomerShippingAddressRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="customerAddressId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getCustomerShippingAddressResponse
  This is the response to getCustomerShippingAddressRequest.
  ===================================================================
  -->
  <xs:element name="getCustomerShippingAddressResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <!-- address and customerProfileId will only be present if a shipping address was successfully retrieved. -->
            <xs:element name="defaultShippingAddress" type="xs:boolean" minOccurs="0"/>
            <xs:element name="address" type="anet:customerAddressExType" minOccurs="0"/>
            <xs:element name="subscriptionIds" type="anet:SubscriptionIdList" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  updateCustomerProfileRequest
  This method is used to update an existing customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="updateCustomerProfileRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="profile" type="anet:customerProfileInfoExType" />
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  updateCustomerProfileResponse
  This is the response to updateCustomerProfileRequest.
  ===================================================================
  -->
  <xs:element name="updateCustomerProfileResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  updateCustomerPaymentProfileRequest
  This method is used to update an existing customer payment profile for a customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="updateCustomerPaymentProfileRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="paymentProfile" type="anet:customerPaymentProfileExType"/>
            <xs:element name="validationMode" type="anet:validationModeEnum" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  updateCustomerPaymentProfileResponse
  This is the response to updateCustomerPaymentProfileRequest.
  ===================================================================
  -->
  <xs:element name="updateCustomerPaymentProfileResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <!-- validationDirectResponse will only be present if validationMode is testMode or liveMode. -->
            <xs:element name="validationDirectResponse" minOccurs="0">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="2048"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  updateCustomerShippingAddressRequest
  This method is used to update an existing customer shipping address for a customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="updateCustomerShippingAddressRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="address" type="anet:customerAddressExType"/>
            <xs:element name="defaultShippingAddress" type="xs:boolean" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  updateCustomerShippingAddressResponse
  This is the response to updateCustomerShippingAddressRequest.
  ===================================================================
  -->
  <xs:element name="updateCustomerShippingAddressResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  deleteCustomerProfileRequest
  This method is used to delete an existing customer profile along with all the 
  customer payment profiles and customer shipping addresses for the customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="deleteCustomerProfileRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  deleteCustomerProfileResponse
  This is the response to deleteCustomerProfileRequest.
  ===================================================================
  -->
  <xs:element name="deleteCustomerProfileResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  deleteCustomerPaymentProfileRequest
  This method is used to delete an existing customer payment profile from a customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="deleteCustomerPaymentProfileRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="customerPaymentProfileId" type="anet:numericString"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  deleteCustomerPaymentProfileResponse
  This is the response to deleteCustomerPaymentProfileRequest.
  ===================================================================
  -->
  <xs:element name="deleteCustomerPaymentProfileResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  deleteCustomerShippingAddressRequest
  This method is used to delete an existing customer shipping address from a customer profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="deleteCustomerShippingAddressRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="customerAddressId" type="anet:numericString"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  deleteCustomerShippingAddressResponse
  This is the response to deleteCustomerShippingAddressRequest.
  ===================================================================
  -->
  <xs:element name="deleteCustomerShippingAddressResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  createCustomerProfileTransactionRequest
  This method is used to generate a payment transaction for a customer payment profile.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="createCustomerProfileTransactionRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="transaction" type="anet:profileTransactionType"/>
            <xs:element name="extraOptions" minOccurs="0">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="1024"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  createCustomerProfileTransactionResponse
  This is the response to createCustomerProfileTransactionRequest.
  ===================================================================
  -->
  <xs:element name="createCustomerProfileTransactionResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="transactionResponse" type="anet:transactionResponse" minOccurs="0"/>
            <xs:element name="directResponse" minOccurs="0">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="2048"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  validateCustomerPaymentProfileRequest
  This method is used to check a customer payment profile by generating a test transaction for it.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="validateCustomerPaymentProfileRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="customerPaymentProfileId" type="anet:numericString"/>
            <xs:element name="customerShippingAddressId" type="anet:numericString" minOccurs="0"/>
            <xs:element name="cardCode" type="anet:cardCode" minOccurs="0" maxOccurs="1"/>
            <xs:element name="validationMode" type="anet:validationModeEnum"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  validateCustomerPaymentProfileResponse
  This is the response to validateCustomerPaymentProfileRequest.
  ===================================================================
  -->
  <xs:element name="validateCustomerPaymentProfileResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="directResponse" minOccurs="0">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="2048"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getCustomerProfileIdsRequest
  This method is used retrieve the customer profile ids for your account in case they get lost.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="getCustomerProfileIdsRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getCustomerProfileIdsResponse
  This is the response to getCustomerProfileIdsRequest.
  ===================================================================
  -->
  <xs:element name="getCustomerProfileIdsResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="ids" type="anet:ArrayOfNumericString"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  updateSplitTenderGroupRequest
  This method is used to void or release an order after getting a partial authorization for a transaction.
  ===================================================================
  -->
  <xs:element name="updateSplitTenderGroupRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="splitTenderId" type="xs:string"/>
            <xs:element name="splitTenderStatus" type="anet:splitTenderStatusEnum"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  updateSplitTenderGroupResponse
  This is the response to updateSplitTenderGroupRequest.
  ===================================================================
  -->
  <xs:element name="updateSplitTenderGroupResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getTransactionDetailsRequest
  This method is used to retrieve detailed information about a single transaction.
  ===================================================================
  -->
  <xs:element name="getTransactionDetailsRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="transId" type="anet:numericString"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getTransactionDetailsResponse
  This is the response to getTransactionDetailsRequest.
  ===================================================================
  -->
  <xs:element name="getTransactionDetailsResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="transaction" type="anet:transactionDetailsType"/>
            <!-- Identifier of the API client application/SDK/library/binding e.g. PHP-SDK-1.0.9.1, ACTIVE_MERCHANT, etc. -->
            <xs:element name="clientId" minOccurs="0">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="30"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="transrefId" minOccurs="0">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:maxLength value="20"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  createTransactionRequest
  This method is used to process transactions.
  ===================================================================
  -->
  <xs:element name="createTransactionRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="transactionRequest" type="anet:transactionRequestType"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
  ===================================================================
  createTransactionResponse
  This is the response that will be returned to a client following
  any type of transaction request.
  ===================================================================
    -->
  <xs:element name="createTransactionResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="transactionResponse" type="anet:transactionResponse"/>
            <xs:element name="profileResponse" type="anet:createProfileResponse" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  heldTransactionRequestType
  These are the fields that are used for a FDS transaction request.
  ===================================================================
  -->
  <xs:complexType name="heldTransactionRequestType">
    <xs:sequence>
      <xs:element name="action" type="anet:afdsTransactionEnum" minOccurs="1" maxOccurs="1"/>
      <xs:element name="refTransId" type="xs:string" minOccurs="1" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 
  ===================================================================
  updateHeldTransactionRequest
  This method is used to approve or decline suspicious transactions.
  ===================================================================
  -->
  <xs:element name="updateHeldTransactionRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="heldTransactionRequest" type="anet:heldTransactionRequestType" minOccurs="1" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
  ===================================================================
  updateHeldTransactionResponse
  This is the response that will be returned to a client following
  updateHeldTransactionRequest.
  ===================================================================
  -->
  <xs:element name="updateHeldTransactionResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="transactionResponse" type="anet:transactionResponse" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getBatchStatisticsRequest
  This method is used to get the batch details for the specified BatchId
  ===================================================================
  -->
  <xs:element name="getBatchStatisticsRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="batchId" type="anet:numericString"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getBatchStatisticsResponse
  This is the response to getBatchStatisticsRequest.
  ===================================================================
  -->
  <xs:element name="getBatchStatisticsResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="batch" type="anet:batchDetailsType" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getSettledBatchListRequest
  This method is used to retrieve a list of settled batches.
  ===================================================================
  -->
  <xs:element name="getSettledBatchListRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="includeStatistics" type="xs:boolean" minOccurs="0"/>
            <xs:element name="firstSettlementDate" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="lastSettlementDate" type="xs:dateTime" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getSettledBatchListResponse
  This is the response to getSettledBatchListRequest.
  ===================================================================
  -->
  <xs:element name="getSettledBatchListResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="batchList" type="anet:ArrayOfBatchDetailsType" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getTransactionListRequest
  This method is used to retrieve a list of settled transactions.
  ===================================================================
  -->
  <xs:element name="getTransactionListRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="batchId" type="anet:numericString" minOccurs="0"/>
            <xs:element name="sorting" type="anet:TransactionListSorting" minOccurs="0" maxOccurs="1"/>
            <xs:element name="paging" type="anet:Paging" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getTransactionListResponse
  This is the response to getTransactionListRequest.
  ===================================================================
  -->
  <xs:element name="getTransactionListResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="transactions" type="anet:ArrayOfTransactionSummaryType" minOccurs="0"/>
            <xs:element name="totalNumInResultSet" type="xs:int" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getHostedProfilePageRequest
  This method is used to give access to the hosted customer profile page to one of your customers.
  The merchant must be signed up for the CIM service to use it.
  ===================================================================
  -->
  <xs:element name="getHostedProfilePageRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="hostedProfileSettings" type="anet:ArrayOfSetting" minOccurs="0">
              <xs:annotation>
                <xs:documentation>Allowed values for settingName are: hostedProfileReturnUrl, hostedProfileReturnUrlText, hostedProfilePageBorderVisible, hostedProfileIFrameCommunicatorUrl, hostedProfileHeadingBgColor, hostedProfileBillingAddressRequired, hostedProfileCardCodeRequired, hostedProfileBillingAddressOptions, hostedProfileManageOptions, hostedProfilePaymentOptions, hostedProfileSaveButtonText.</xs:documentation>
              </xs:annotation>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getHostedProfilePageResponse
  This is the response to getHostedProfilePageRequest.
  ===================================================================
  -->
  <xs:element name="getHostedProfilePageResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="token" type="xs:string"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getUnsettledTransactionListRequest
  This method is used to retrieve a list of unsettled transactions.
  ===================================================================
  -->
  <xs:element name="getUnsettledTransactionListRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="status" type="anet:TransactionGroupStatusEnum" minOccurs="0" maxOccurs="1"/>
            <xs:element name="sorting" type="anet:TransactionListSorting" minOccurs="0" maxOccurs="1"/>
            <xs:element name="paging" type="anet:Paging" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
  ===================================================================
  getHostedPaymentPageRequest
  This method is used to give access to the hosted payment page to one of your customers.
  ===================================================================
  -->
  <xs:element name="getHostedPaymentPageRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="transactionRequest" type="anet:transactionRequestType"/>
            <xs:element name="hostedPaymentSettings" type="anet:ArrayOfSetting" minOccurs="0">
              <xs:annotation>
                <xs:documentation>Allowed values for settingName are: hostedPaymentIFrameCommunicatorUrl, hostedPaymentButtonOptions, hostedPaymentReturnOptions, hostedPaymentOrderOptions, hostedPaymentPaymentOptions, hostedPaymentBillingAddressOptions, hostedPaymentShippingAddressOptions, hostedPaymentSecurityOptions, hostedPaymentCustomerOptions, hostedPaymentStyleOptions</xs:documentation>
              </xs:annotation>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  getHostedPaymentPageResponse
  This is the response to getHostedPaymentPageRequest.
  ===================================================================
  -->
  <xs:element name="getHostedPaymentPageResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="token" type="xs:string"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:simpleType name="TransactionListOrderFieldEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="id"/>
      <xs:enumeration value="submitTimeUTC"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="TransactionListSorting">
    <xs:sequence>
      <xs:element name="orderBy" type="anet:TransactionListOrderFieldEnum" minOccurs="1" maxOccurs="1"/>
      <xs:element name="orderDescending" type="xs:boolean" minOccurs="1" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 
  ===================================================================
  getUnsettledTransactionListResponse
  This is the response to getUnsettledTransactionListRequest.
  ===================================================================
  -->
  <xs:element name="getUnsettledTransactionListResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="transactions" type="anet:ArrayOfTransactionSummaryType" minOccurs="0"/>
            <xs:element name="totalNumInResultSet" type="xs:int" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
  ===================================================================
  mobileDeviceRegistrationRequest
  This method is used to request registration for a mobile device.
  ===================================================================
  -->
  <xs:element name="mobileDeviceRegistrationRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="mobileDevice" type="anet:mobileDeviceType" minOccurs="1" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  mobileDeviceRegistrationResponse
  This is the response to mobileDeviceRegistrationRequest.
  ===================================================================
  -->
  <xs:element name="mobileDeviceRegistrationResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
  ===================================================================
  mobileDeviceLoginRequest
  This method is used to authenticate a mobile device.
  ===================================================================
  -->
  <xs:element name="mobileDeviceLoginRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  mobileDeviceLoginResponse
  This is the response to mobileDeviceLoginRequest.
  ===================================================================
  -->
  <xs:element name="mobileDeviceLoginResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="merchantContact" type="anet:merchantContactType"/>
            <xs:element name="userPermissions" type="anet:ArrayOfPermissionType"/>
            <xs:element name="merchantAccount" type="anet:transRetailInfoType" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
  ===================================================================
  logoutRequest
  This method is used to end a session from a mobile device.
  ===================================================================
  -->
  <xs:element name="logoutRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  logoutResponse
  This is the response to logoutRequest.
  ===================================================================
  -->
  <xs:element name="logoutResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  sendCustomerTransactionReceiptRequest
  This method is used to send a transaction email receipt.
  ===================================================================
  -->
  <xs:element name="sendCustomerTransactionReceiptRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="transId" type="anet:numericString"/>
            <xs:element name="customerEmail" type="xs:string"/>
            <xs:element name="emailSettings" type="anet:emailSettingsType" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  sendCustomerTransactionReceiptResponse
  This is the response to sendCustomerTransactionReceiptRequest.
  ===================================================================
  -->
  <xs:element name="sendCustomerTransactionReceiptResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- 
  ===================================================================
  PayPal Payment Type.
  ===================================================================
  -->
  <xs:complexType name="payPalType">
    <xs:sequence>
      <xs:element name="successUrl" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="2048"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="cancelUrl" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="2048"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="paypalLc" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="2"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="paypalHdrImg" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="127"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="paypalPayflowcolor" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="6"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="payerID" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ARBGetSubscriptionListRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="searchType" type="anet:ARBGetSubscriptionListSearchTypeEnum" minOccurs="1" maxOccurs="1"/>
            <xs:element name="sorting" type="anet:ARBGetSubscriptionListSorting" minOccurs="0" maxOccurs="1"/>
            <xs:element name="paging" type="anet:Paging" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="Paging">
    <xs:sequence>
      <xs:element name="limit" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:int">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="1000"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="offset" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:int">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="100000"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ARBGetSubscriptionListSorting">
    <xs:sequence>
      <xs:element name="orderBy" type="anet:ARBGetSubscriptionListOrderFieldEnum" minOccurs="1" maxOccurs="1"/>
      <xs:element name="orderDescending" type="xs:boolean" minOccurs="1" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="ARBGetSubscriptionListSearchTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="cardExpiringThisMonth"/>
      <xs:enumeration value="subscriptionActive"/>
      <xs:enumeration value="subscriptionExpiringThisMonth"/>
      <xs:enumeration value="subscriptionInactive"/>
      <!-- AboutExpire -->
      <!-- ActiveCard -->
      <!-- WillExpire -->
      <!-- Inactive -->
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ARBGetSubscriptionListOrderFieldEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="id"/>
      <xs:enumeration value="name"/>
      <xs:enumeration value="status"/>
      <xs:enumeration value="createTimeStampUTC"/>
      <xs:enumeration value="lastName"/>
      <xs:enumeration value="firstName"/>
      <xs:enumeration value="accountNumber"/>
      <xs:enumeration value="amount"/>
      <xs:enumeration value="pastOccurrences"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="ARBGetSubscriptionListResponse">
    <xs:complexType mixed="false">
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="totalNumInResultSet" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="subscriptionDetails" type="anet:ArrayOfSubscription" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ArrayOfSubscription">
    <xs:sequence>
      <xs:element name="subscriptionDetail" type="anet:SubscriptionDetail" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SubscriptionDetail">
    <xs:sequence>
      <xs:element name="id" type="xs:int" minOccurs="1" maxOccurs="1"/>
      <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="status" type="anet:ARBSubscriptionStatusEnum" minOccurs="1" maxOccurs="1"/>
      <xs:element name="createTimeStampUTC" type="xs:dateTime" minOccurs="1" maxOccurs="1"/>
      <xs:element name="firstName" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="lastName" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="totalOccurrences" type="xs:int" minOccurs="1" maxOccurs="1"/>
      <xs:element name="pastOccurrences" type="xs:int" minOccurs="1" maxOccurs="1"/>
      <xs:element name="paymentMethod" type="anet:paymentMethodEnum" minOccurs="1" maxOccurs="1"/>
      <xs:element name="accountNumber" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="invoice" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="amount" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0.00"/>
            <xs:fractionDigits value="4"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="currencyCode" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="customerProfileId" type="xs:int" minOccurs="1" maxOccurs="1"/>
      <xs:element name="customerPaymentProfileId" type="xs:int" minOccurs="1" maxOccurs="1"/>
      <xs:element name="customerShippingProfileId" type="xs:int" minOccurs="0" maxOccurs="1"/>
      <!--
      <xs:element minOccurs="1" maxOccurs="1" name="currencyId" type="xs:int" />
      <xs:element minOccurs="1" maxOccurs="1" name="ARBStatusId" type="xs:int" />
      <xs:element minOccurs="1" maxOccurs="1" name="PaymentMethodType" type="xs:int" />
      -->
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="customerProfileSummaryType">
    <xs:sequence>
      <xs:element name="customerProfileId" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="merchantCustomerId" type="xs:string" minOccurs="1" maxOccurs="1"/>
      <xs:element name="email" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="createdDate" type="xs:dateTime"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Enums used in some complextypes that are not directly referenced -->
  <xs:element name="EnumCollection">
    <xs:complexType>
      <xs:sequence>
        <!-- classes -->
        <xs:element name="customerProfileSummaryType" type="anet:customerProfileSummaryType" minOccurs="1" maxOccurs="1"/>
        <xs:element name="paymentSimpleType" type="anet:paymentSimpleType" minOccurs="1" maxOccurs="1"/>
        <!-- enums -->
        <xs:element name="accountTypeEnum" type="anet:accountTypeEnum" minOccurs="1" maxOccurs="1"/>
        <xs:element name="cardTypeEnum" type="anet:cardTypeEnum" minOccurs="1" maxOccurs="1"/>
        <xs:element name="FDSFilterActionEnum" type="anet:FDSFilterActionEnum" minOccurs="1" maxOccurs="1"/>
        <xs:element name="permissionsEnum" type="anet:permissionsEnum" minOccurs="1" maxOccurs="1"/>
        <xs:element name="settingNameEnum" type="anet:settingNameEnum" minOccurs="1" maxOccurs="1"/>
        <xs:element name="settlementStateEnum" type="anet:settlementStateEnum" minOccurs="1" maxOccurs="1"/>
        <xs:element name="transactionStatusEnum" type="anet:transactionStatusEnum" minOccurs="1" maxOccurs="1"/>
        <xs:element name="transactionTypeEnum" type="anet:transactionTypeEnum" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <!--
===================================================================
  Customer Profile Payment Type.
  ===================================================================
  -->
  <xs:complexType name="customerProfilePaymentType">
    <xs:sequence>
      <xs:element name="createProfile" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="customerProfileId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
      <xs:element name="paymentProfile" type="anet:paymentProfile" minOccurs="0" maxOccurs="1"/>
      <xs:element name="shippingProfileId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!--
===================================================================
Payment Profile Type.
===================================================================
-->
  <xs:complexType name="paymentProfile">
    <xs:sequence>
      <xs:element name="paymentProfileId" type="anet:numericString" minOccurs="1" maxOccurs="1"/>
      <xs:element name="cardCode" type="anet:cardCode" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Element definition for GetCustomerPaymentProfileList -->
  <xs:element name="getCustomerPaymentProfileListRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="searchType" type="anet:CustomerPaymentProfileSearchTypeEnum" minOccurs="1" maxOccurs="1"/>
            <xs:element name="month" minOccurs="1" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:minLength value="4"/>
                  <xs:maxLength value="7"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="sorting" type="anet:CustomerPaymentProfileSorting" minOccurs="0" maxOccurs="1"/>
            <xs:element name="paging" type="anet:Paging" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="getCustomerPaymentProfileListResponse">
    <xs:complexType mixed="false">
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="totalNumInResultSet" type="xs:int" minOccurs="1" maxOccurs="1"/>
            <xs:element name="paymentProfiles" type="anet:arrayOfCustomerPaymentProfileListItemType" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:simpleType name="CustomerPaymentProfileOrderFieldEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="id"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="CustomerPaymentProfileSorting">
    <xs:sequence>
      <xs:element name="orderBy" type="anet:CustomerPaymentProfileOrderFieldEnum" minOccurs="1" maxOccurs="1"/>
      <xs:element name="orderDescending" type="xs:boolean" minOccurs="1" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="CustomerPaymentProfileSearchTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="cardsExpiringInMonth"/>
      <!--  expired or expiring -->
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="customerPaymentProfileListItemType">
    <xs:sequence>
      <xs:element name="defaultPaymentProfile" type="xs:boolean" minOccurs="0"/>
      <xs:element name="customerPaymentProfileId" type="xs:int" minOccurs="1" maxOccurs="1"/>
      <xs:element name="customerProfileId" type="xs:int" minOccurs="1" maxOccurs="1"/>
      <xs:element name="billTo" type="anet:customerAddressType" minOccurs="1" maxOccurs="1"/>
      <xs:element name="payment" type="anet:paymentMaskedType" minOccurs="1" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="arrayOfCustomerPaymentProfileListItemType">
    <xs:sequence>
      <xs:element name="paymentProfile" type="anet:customerPaymentProfileListItemType" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!--  
  ===================================================================
  ARBGetSubscriptionRequest
  This method is used to get details of an existing ARB subscription.
  The merchant must be signed up for the ARB service to use it.
  ===================================================================
  -->
  <xs:element name="ARBGetSubscriptionRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="subscriptionId" type="anet:numericString" minOccurs="1" maxOccurs="1"/>
            <xs:element name="includeTransactions" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
===================================================================
Get ARBSubscription Response 
===================================================================
-->
  <xs:element name="ARBGetSubscriptionResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="subscription" type="anet:ARBSubscriptionMaskedType" minOccurs="1" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
===================================================================
Get TransactionList For Customer Request
This method is used to retrieve a list of transactions for a customer profile, and optionally, a customer payment profile.
The merchant must be signed up for the CIM service to use it.
===================================================================
-->
  <xs:element name="getTransactionListForCustomerRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="customerProfileId" type="anet:numericString"/>
            <xs:element name="customerPaymentProfileId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
            <xs:element name="sorting" type="anet:TransactionListSorting" minOccurs="0" maxOccurs="1"/>
            <xs:element name="paging" type="anet:Paging" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
  ===================================================================
  Customer Profile Subscription Payment Type.
  ===================================================================
  -->
  <xs:complexType name="customerProfileIdType">
    <xs:sequence>
      <xs:element name="customerProfileId" type="anet:numericString" minOccurs="1" maxOccurs="1"/>
      <xs:element name="customerPaymentProfileId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
      <xs:element name="customerAddressId" type="anet:numericString" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!--  
  ===================================================================
  Get AUJobSummary Request
  This method is used to get summary of jobs done by Account Updater.
  ===================================================================
  -->
  <xs:element name="getAUJobSummaryRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="month" minOccurs="1" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:minLength value="4"/>
                  <xs:maxLength value="7"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
  ===================================================================
  Get AUJobSummary Response
  ===================================================================
  -->
  <xs:element name="getAUJobSummaryResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="auSummary" type="anet:ArrayOfAUResponseType" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- ===================================================== -->
  <xs:complexType name="ArrayOfAUResponseType">
    <xs:sequence>
      <xs:element name="auResponse" type="anet:auResponseType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="auResponseType">
    <xs:sequence>
      <xs:element name="auReasonCode" type="xs:string"/>
      <xs:element name="profileCount" type="xs:long"/>
      <xs:element name="reasonDescription" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <!--  
  ===================================================================
  Get AUJobDetails Request
  This method is used to get summary of jobs done by Account Updater.
  ===================================================================
  -->
  <xs:element name="getAUJobDetailsRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="month" minOccurs="1" maxOccurs="1">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:minLength value="4"/>
                  <xs:maxLength value="7"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="modifiedTypeFilter" type="anet:AUJobTypeEnum" minOccurs="0" maxOccurs="1"/>
            <xs:element name="paging" type="anet:Paging" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- =========================================================== -->
  <xs:simpleType name="AUJobTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="all"/>
      <xs:enumeration value="updates"/>
      <xs:enumeration value="deletes"/>
      <!-- All Entries -->
      <!-- Only Updated Entries -->
      <!-- Only Deleted Entries -->
    </xs:restriction>
  </xs:simpleType>
  <!--
  ===================================================================
  Get AUJobDetails Response
  ===================================================================
  -->
  <xs:element name="getAUJobDetailsResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="totalNumInResultSet" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="auDetails" type="anet:ListOfAUDetailsType" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- ===================================================== -->
  <xs:complexType name="ListOfAUDetailsType">
    <xs:choice maxOccurs="unbounded">
      <xs:element name="auUpdate" type="anet:auUpdateType" minOccurs="0"/>
      <xs:element name="auDelete" type="anet:auDeleteType" minOccurs="0"/>
    </xs:choice>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="auUpdateType">
    <xs:complexContent>
      <xs:extension base="anet:auDetailsType">
        <xs:sequence>
          <xs:element name="newCreditCard" type="anet:creditCardMaskedType"/>
          <xs:element name="oldCreditCard" type="anet:creditCardMaskedType"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="auDeleteType">
    <xs:complexContent>
      <xs:extension base="anet:auDetailsType">
        <xs:sequence>
          <xs:element name="creditCard" type="anet:creditCardMaskedType"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ===================================================== -->
  <xs:complexType name="auDetailsType">
    <xs:sequence>
      <xs:element name="customerProfileID" type="xs:long"/>
      <xs:element name="customerPaymentProfileID" type="xs:long"/>
      <xs:element name="firstName" type="xs:string" minOccurs="0"/>
      <xs:element name="lastName" type="xs:string" minOccurs="0"/>
      <xs:element name="updateTimeUTC" type="xs:string"/>
      <xs:element name="auReasonCode" type="xs:string"/>
      <xs:element name="reasonDescription" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <!--  
  ===================================================================
  Get MerchantDetails Request
  This method is used to get merchant capabilities.
  ===================================================================
  -->
  <xs:element name="getMerchantDetailsRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!-- ===================================================== -->
  <xs:simpleType name="currencyCodeType">
    <xs:restriction base="xs:string">
      <xs:minLength value="3"/>
      <xs:maxLength value="3"/>
    </xs:restriction>
  </xs:simpleType>
  <!--  
  ==============================================================================
  Processing Options type
  Processing Options are used to pass additional transaction processing options
  ==============================================================================
  -->
  <xs:complexType name="processingOptions">
    <xs:sequence>
      <xs:element name="isFirstRecurringPayment"  type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="isFirstSubsequentAuth"    type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="isSubsequentAuth"         type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="isStoredCredentials"      type="xs:boolean" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!--  
  ==============================================================================
  Subsequent Auth Information type
  Subsequent Auth Information is used to pass additional information for 
  Merchant-Initiated transactions
  ==============================================================================
  -->
  <xs:complexType name="subsequentAuthInformation">
    <xs:sequence>
      <xs:element name="originalNetworkTransId" type="anet:networkTransId" minOccurs="0" maxOccurs="1"/>
      <xs:element name="reason"                 type="anet:merchantInitTransReasonEnum" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!--  
  ==============================================================================
  Credit Card processing network transaction id
  ==============================================================================
  -->
  <xs:simpleType name="networkTransId">
    <xs:restriction base="anet:alphaNumericString">
      <xs:maxLength value="255"/>
    </xs:restriction>
  </xs:simpleType>
  <!--  
  ==============================================================================
  Reason for Merchant Initiated Transaction
  ==============================================================================
  -->
  <xs:simpleType name="merchantInitTransReasonEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="resubmission"/>
      <xs:enumeration value="delayedCharge"/>
      <xs:enumeration value="reauthorization"/>
      <xs:enumeration value="noShow"/>
    </xs:restriction>
  </xs:simpleType>
  
  <!--
  ===================================================================
  ArrayOfCurrencyCode
  =================================================================== 
  -->
  <xs:complexType name="ArrayOfCurrencyCode">
    <xs:sequence>
      <xs:element name="currency" type="anet:currencyCodeType" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!--
  ===================================================================
  ArrayOfCardType
  =================================================================== 
  -->
  <xs:complexType name="ArrayOfCardType">
    <xs:sequence>
      <xs:element name="cardType" type="xs:string" nillable="true" minOccurs="0" maxOccurs="30"/>
    </xs:sequence>
  </xs:complexType>
  
  <!--============= processorType ======================================================-->
  <xs:complexType name="processorType">
    <xs:sequence>
      <xs:element name="name">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="id" type="xs:int"></xs:element>
      <xs:element name="cardTypes" type ="anet:ArrayOfCardType" minOccurs="0" maxOccurs="1" ></xs:element>
    </xs:sequence>
  </xs:complexType>
  <!--
  ===================================================================
  ArrayOfProcessorType
  =================================================================== 
  -->
  <xs:complexType name="ArrayOfProcessorType">
    <xs:sequence>
      <xs:element name="processor" type="anet:processorType" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!--
  ===================================================================
  marketType
  Name of the marketType. 
  ===================================================================
  -->
  <xs:simpleType name="marketType">
    <xs:restriction base="xs:string">
      <xs:maxLength value="50"/>
    </xs:restriction>
  </xs:simpleType>
  <!--
  ===================================================================
  ArrayOfMarketType
  =================================================================== 
  -->
  <xs:complexType name="ArrayOfMarketType">
    <xs:sequence>
      <xs:element name="marketType" type="anet:marketType" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!--
  ===================================================================
  productCodeType
  Name of the productCode. 
  ===================================================================
  -->
  <xs:simpleType name="productCodeType">
    <xs:restriction base="xs:string">
      <xs:maxLength value="3"/>
    </xs:restriction>
  </xs:simpleType>
  <!--
  ===================================================================
  ArrayOfProductCode
  =================================================================== 
  -->
  <xs:complexType name="ArrayOfProductCode">
    <xs:sequence>
      <xs:element name="productCode" type="anet:productCodeType" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 
  ===================================================================
  paymentMethodsType represents the array of payment methods.
  ===================================================================
  -->
  <xs:simpleType name="paymentMethodsTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Visa"/>
      <xs:enumeration value="MasterCard"/>
      <xs:enumeration value="Discover"/>
      <xs:enumeration value="AmericanExpress"/>
      <xs:enumeration value="DinersClub"/>
      <xs:enumeration value="JCB"/>
      <xs:enumeration value="EnRoute"/>
      <xs:enumeration value="Echeck"/>
      <xs:enumeration value="Paypal"/>
      <xs:enumeration value="VisaCheckout"/>
      <xs:enumeration value="ApplePay"/>
      <xs:enumeration value="AndroidPay"/>
    </xs:restriction>
  </xs:simpleType>
  <!--
  ===================================================================
  ArrayOfPaymentMethod
  =================================================================== 
  -->
  <xs:complexType name="ArrayOfPaymentMethod">
    <xs:sequence>
      <xs:element name="paymentMethod" type="anet:paymentMethodsTypeEnum" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="webCheckOutDataTypeToken">
    <xs:sequence>
      <xs:element name="cardNumber" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="4"/>
            <xs:maxLength value="16"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="expirationDate" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="4"/>
            <xs:maxLength value="7"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="cardCode" type="anet:cardCode" minOccurs="0" maxOccurs="1"/>
      <xs:element name="zip" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="fullName" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="64"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <!--
  ===================================================================
  Get MerchantDetails Response
  ===================================================================
  -->
  <xs:element name="getMerchantDetailsResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse">
          <xs:sequence>
            <xs:element name="isTestMode" type="xs:boolean" minOccurs="0"/>
            <xs:element name="processors" type="anet:ArrayOfProcessorType"/>
            <xs:element name="merchantName" type="xs:string"/>
            <xs:element name="gatewayId" type="anet:numericString"/>
            <xs:element name="marketTypes" type="anet:ArrayOfMarketType"/>
            <xs:element name="productCodes" type="anet:ArrayOfProductCode"/>
            <xs:element name="paymentMethods" type="anet:ArrayOfPaymentMethod"/>
            <xs:element name="currencies" type="anet:ArrayOfCurrencyCode"/>
            <xs:element name="publicClientKey" type="xs:string" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--  
  ===================================================================
  Update MerchantDetails Request
  This method is used for a partner to update merchant details.
  ===================================================================
  -->
  <xs:element name="updateMerchantDetailsRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiRequest">
          <xs:sequence>
            <xs:element name="isTestMode" type="xs:boolean" minOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--  
  ===================================================================
  Update MerchantDetails Response
  This method is used for a partner to update merchant details.
  ===================================================================
  -->
  <xs:element name="updateMerchantDetailsResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="anet:ANetApiResponse"/>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
</xs:schema>
