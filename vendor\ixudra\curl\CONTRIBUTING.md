# Contributing

Contributions are **welcome** and will be fully **credited**.

Please read and understand the contribution guide before creating an issue or pull request.


## Etiquette

This project is open source, and as such, the maintainers give their free time to build and maintain the source code held within. They make the code freely available in the hope that it will be of use to other developers. It would be extremely unfair for them to suffer abuse or anger for their hard work.

Please be considerate towards maintainers when raising issues or presenting pull requests. Let's show the world that developers are civilized and selfless people.


## Creating issues

### Procedure

Before filing an issue:

- Attempt to replicate the problem, to ensure that it wasn't a coincidental incident
- Reread the documentation to make sure if the answer the question hasn't already been addressed there
- Make sure the question wasn't already asked before in this project. Also check the closed issues
- Check the pull requests tab to make sure that the bug doesn't have a fix in progress


## Submitting pull requests

### Viability

When requesting or submitting new features, first consider whether it might be useful to others. Open
source projects are used by many developers, who may have entirely different needs to your own. Think about
whether or not your feature is likely to be used by other users of the project.


### Procedure

Before submitting a pull request:

- Check the codebase to ensure that your feature doesn't already exist
- Check the pull requests tab to make sure that the bug doesn't have a feature request in progress


### Requirements

If the project maintainer has any additional requirements, you will find them listed here:

- **Respect the project code style** - Your pull request will not be accepted if it changes half of the lines in the files. All of our projects generally adhere to the [PSR-2 Coding Standard](https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-2-coding-style-guide.md) with some minor differences
- **Make sure all tests are updated** - Unit tests are out insurance policy that guarantee that the code functions as intended. Please make sure they function correctly and cover all changes you have made to the code
- **Document any change in behaviour** - Make sure the `README.md` and any other relevant documentation are kept up-to-date
- **One pull request per feature** - If you want to do more than one thing, send multiple pull requests
- **Send coherent history** - Make sure each individual commit in your pull request is meaningful. If you had to make multiple intermediate commits while developing, please [squash them](http://www.git-scm.com/book/en/v2/Git-Tools-Rewriting-History#Changing-Multiple-Commit-Messages) before submitting

**Happy coding**!
