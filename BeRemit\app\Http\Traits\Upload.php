<?php

namespace App\Http\Traits;

use Intervention\Image\Facades\Image;

trait Upload
{
    public function makeDirectory($path)
    {
        if (file_exists($path)) return true;
        return mkdir($path, 0755, true);
    }

    public function removeFile($path)
    {
        return file_exists($path) && is_file($path) ? @unlink($path) : false;
    }

    public function uploadImage($file, $location, $size = null, $old = null, $thumb = null, $filename = null)
    {
        $path = $this->makeDirectory($location);
        if (!$path) throw new \Exception('File could not been created.');

        if (!empty($old)) {
            $this->removeFile($location . '/' . $old);
            $this->removeFile($location . '/thumb_' . $old);
        }

        if ($filename == null) {
            $filename = uniqid() . time() . '.' . $file->getClientOriginalExtension();
        }

        if(strtolower($file->getClientOriginalExtension()) == 'pdf'){
            $file->move($location, $filename);
        }else{
            $image = Image::make($file);

            if (!empty($size)) {
                $size = explode('x', strtolower($size));
                $image->resize($size[0], $size[1]);
            }
            $image->save($location . '/' . $filename);

            if (!empty($thumb)) {
                $thumb = explode('x', $thumb);
                Image::make($file)->resize($thumb[0], $thumb[1])->save($location . '/thumb_' . $filename);
            }
        }


        return $filename;
    }


}

