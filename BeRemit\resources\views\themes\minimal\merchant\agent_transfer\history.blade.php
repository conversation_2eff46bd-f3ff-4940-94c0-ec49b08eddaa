@extends($theme.'layouts.merchant')
@section('title', trans($title))

@section('content')
    <div class="container-fluid">
        <div class="main-page">
            <div class="row">
                <div class="col-12">
                    <div class="card card-primary shadow">
                        <div class="card-header bg-primary">
                            <h3 class="text-white">@lang('Sent Transfers')</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                        <tr>
                                            <th>@lang('Date')</th>
                                            <th>@lang('TRX ID')</th>
                                            <th>@lang('Recipient')</th>
                                            <th>@lang('Amount')</th>
                                            <th>@lang('Fee')</th>
                                            <th>@lang('Final Amount')</th>
                                            <th>@lang('Note')</th>
                                            <th>@lang('Status')</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($sent_transfers as $transfer)
                                            <tr>
                                                <td>{{ dateTime($transfer->created_at) }}</td>
                                                <td>{{ $transfer->trx_id }}</td>
                                                <td>{{ optional($transfer->receiver)->fullname }}</td>
                                                <td>{{ getAmount($transfer->amount) }} {{ config('basic.currency') }}</td>
                                                <td>{{ getAmount($transfer->fee) }} {{ config('basic.currency') }}</td>
                                                <td>{{ getAmount($transfer->final_amount) }} {{ config('basic.currency') }}</td>
                                                <td>{{ $transfer->note ?? 'N/A' }}</td>
                                                <td>
                                                    @if($transfer->status == 1)
                                                        <span class="badge badge-success">@lang('Completed')</span>
                                                    @else
                                                        <span class="badge badge-danger">@lang('Failed')</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="8" class="text-center">@lang('No transfers found')</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                                {{ $sent_transfers->links() }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 mt-4">
                    <div class="card card-primary shadow">
                        <div class="card-header bg-primary">
                            <h3 class="text-white">@lang('Received Transfers')</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                        <tr>
                                            <th>@lang('Date')</th>
                                            <th>@lang('TRX ID')</th>
                                            <th>@lang('Sender')</th>
                                            <th>@lang('Amount')</th>
                                            <th>@lang('Note')</th>
                                            <th>@lang('Status')</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($received_transfers as $transfer)
                                            <tr>
                                                <td>{{ dateTime($transfer->created_at) }}</td>
                                                <td>{{ $transfer->trx_id }}</td>
                                                <td>{{ optional($transfer->sender)->fullname }}</td>
                                                <td>{{ getAmount($transfer->final_amount) }} {{ config('basic.currency') }}</td>
                                                <td>{{ $transfer->note ?? 'N/A' }}</td>
                                                <td>
                                                    @if($transfer->status == 1)
                                                        <span class="badge badge-success">@lang('Completed')</span>
                                                    @else
                                                        <span class="badge badge-danger">@lang('Failed')</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="6" class="text-center">@lang('No transfers found')</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                                {{ $received_transfers->links() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('css-lib')
@endpush

@push('extra-js')
@endpush
