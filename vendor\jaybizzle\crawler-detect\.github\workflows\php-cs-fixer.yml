name: Check & fix styling

on: [ push ]

jobs:
    php-cs-fixer:
        runs-on: ubuntu-latest

        steps:
            -   name: Checkout code
                uses: actions/checkout@v2
                with:
                    ref: ${{ github.head_ref }}

            -   name: Run PHP CS Fixer
                uses: docker://oskarstark/php-cs-fixer-ga:2.18.6
                with:
                    args: --config=.php_cs.dist --allow-risky=yes

            -   name: Commit changes
                uses: stefanzweifel/git-auto-commit-action@v4
                with:
                    commit_message: Fix styling