<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Mail\SendMail;
use App\Models\User;
use App\Models\UserLogin;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required',
            'password' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }
        $user = User::where('username', trim(strtolower($request->username)))->orWhere('email', trim(strtolower($request->username)))->first();
        if (!$user) {
            $result['status'] = false;
            $result['message'] = 'User does not exist!';
            $result['data'] = [];
            return response($result, 200);
        }

        if (Hash::check($request->password, $user->password)) {
            if ($user->status == 0) {
                $result['status'] = false;
                $result['message'] = 'Account Has been Suspended';
                $result['data'] = [];
                return response($result, 200);
            }

			/*
            if ($user->merchant == 1) {
                $result['status'] = false;
                $result['message'] = 'You have to use merchant app';
                $result['data'] = [];
                return response($result, 200);
            }
			*/
			
            $user->last_login = Carbon::now();
            $user->last_login_ip = request()->ip();
            $user->save();


            $info = @json_decode(json_encode(getIpInfo()), true);
            $ul['user_id'] = $user->id;
            $ul['user_ip'] = request()->ip();
            $ul['longitude'] = @implode(',', @$info['long']);
            $ul['latitude'] = @implode(',', @$info['lat']);
            $ul['location'] = @implode(',', @$info['city']) . (" - " . @implode(',', @$info['area']) . "- ") . @implode(',', $info['country']) . (" - " . @implode(',', $info['code']) . " ");
            $ul['country_code'] = @implode(',', @$info['code']);
            $ul['browser'] = @$info['browser'];
            $ul['os'] = @$info['os_platform'];
            $ul['country'] = @implode(',', @$info['country']);
            UserLogin::create($ul);

            $token = $user->createToken('Laravel Password Grant Client')->accessToken;

            $response = ['token' => $token, 'user' => $user];
            $result['status'] = true;
            $result['message'] = 'Login Successfully';
            $result['data'] = $response;
            return response($result, 200);

        } else {
            $result['status'] = false;
            $result['message'] = 'Incorrect Password';
            $result['data'] = [];
            return response($result, 200);
        }

    }

    public function username()
    {
        $login = request()->input('username');
        $field = filter_var($login, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
        request()->merge([$field => $login]);
        return $field;
    }

    public function getEmailForRecoverPass(Request $request)
    {
        $validateUser = Validator::make($request->all(),
            [
                'email' => 'required|email',
            ]);

        if ($validateUser->fails()) {
            $result['status'] = false;
            $result['message'] = $validateUser->errors();
            return response($result, 200);
        }

        try {
            $user = User::where('email', $request->email)->first();
            if (!$user) {
                $result['status'] = false;
                $result['message'] = 'Email does not exit on record';
                return response($result, 200);
            }

            $code = rand(10000, 99999);
            $data['email'] = $request->email;
            $data['message'] = 'OTP has been send';
            $user->verify_code = $code;
            $user->save();

            $basic = basicControl();
            $message = 'Your Password Recovery Code is ' . $code;
            $email_from = $basic->sender_email;
            @Mail::to($request->email)->send(new SendMail($email_from, "Recovery Code", $message));

            $result['status'] = true;
            $result['message'] = $data;
            return response($result, 200);
        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            return response($result, 200);
        }
    }

    public function getCodeForRecoverPass(Request $request)
    {
        $validateUser = Validator::make($request->all(),
            [
                'code' => 'required',
                'email' => 'required|email',
            ]);

        if ($validateUser->fails()) {
            $result['status'] = false;
            $result['message'] = $validateUser->errors();
            return response($result, 200);
        }

        try {
            $user = User::where('email', $request->email)->first();
            if (!$user) {
                $result['status'] = false;
                $result['message'] = 'Email does not exit on record';
                return response($result, 200);
            }

            if ($user->verify_code == $request->code && $user->updated_at > Carbon::now()->subMinutes(5)) {
                $user->verify_code = null;
                $user->save();

                $result['status'] = true;
                $result['message'] = 'Code Matching';
                return response($result, 200);
            }

            $result['status'] = false;
            $result['message'] = 'Invalid Code';
            return response($result, 200);
        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            return response($result, 200);
        }
    }

    public function updatePass(Request $request)
    {
        if (config('basic.strong_password') == 0) {
            $rules['password'] = ['required', 'min:6', 'confirmed'];
        } else {
            $rules['password'] = ["required", 'confirmed',
                Password::min(6)->mixedCase()
                    ->letters()
                    ->numbers()
                    ->symbols()
                    ->uncompromised()];
        }
        $rules['email'] = ['required', 'email'];

        $validateUser = Validator::make($request->all(), $rules);

        if ($validateUser->fails()) {
            $result['status'] = false;
            $result['message'] = $validateUser->errors();
            return response($result, 200);
        }

        $user = User::where('email', $request->email)->first();
        if (!$user) {
            $result['status'] = false;
            $result['message'] = 'Email does not exist on record';
            return response($result, 200);
        }
        $user->password = Hash::make($request->password);
        $user->save();
        $result['status'] = true;
        $result['message'] = 'Password Updated';
        return response($result, 200);
    }

}
